// Dynamically import docx inside functions to keep initial bundle smaller
// import { Document, Packer, Paragraph, TextRun } from 'docx';

/**
 * Parses a DOM node and its children to create an array of docx TextRun objects.
 * This handles basic formatting like bold and italics.
 * @param {Node} node The DOM node to parse.
 * @param {Object} style The style to apply to text runs.
 * @returns {TextRun[]} An array of TextRun objects.
 */
function parseNodeToTextRuns(node, style = {}) {
  const runs = [];
  for (const child of node.childNodes) {
    if (child.nodeType === Node.TEXT_NODE) {
      if (child.textContent) {
        runs.push(new TextRun({ ...style, text: child.textContent }));
      }
    } else if (child.nodeType === Node.ELEMENT_NODE) {
      const newStyle = { ...style };
      if (child.nodeName === 'B' || child.nodeName === 'STRONG') {
        newStyle.bold = true;
      }
      if (child.nodeName === 'I' || child.nodeName === 'EM') {
        newStyle.italics = true;
      }
      if (child.tagName === 'BR') {
        runs.push(new TextRun({ ...newStyle, break: 1 }));
      } else {
        runs.push(...parseNodeToTextRuns(child, newStyle));
      }
    }
  }
  return runs;
}

/**
 * Converts an HTML string into a `docx` Document object that can be packed into a .docx file.
 * @param {string} htmlString The HTML content from the editor.
 * @returns {Promise<Blob>} A promise that resolves with the .docx file as a Blob.
 */
export async function convertHtmlToDocxBlob(htmlString) {
  // Dynamically import docx only when needed
  const { Document, Packer, Paragraph, TextRun } = await import('docx');

  const parser = new DOMParser();
  // Wrap in a div to ensure proper parsing of top-level nodes
  const doc = parser.parseFromString(`<div id="wrapper">${htmlString}</div>`, 'text/html');

  const paragraphs = [];
  const wrapper = doc.getElementById('wrapper');

  if (!wrapper) {
    // Fallback if wrapper not found
    paragraphs.push(new Paragraph({
      children: [new TextRun({ text: htmlString })]
    }));
  } else {
    const nodes = wrapper.childNodes;

    for (const node of nodes) {
      if (node.nodeType === Node.TEXT_NODE) {
        // Handle top-level text nodes
        const text = node.textContent.trim();
        if (text) {
          paragraphs.push(new Paragraph({
            children: [new TextRun({ text })]
          }));
        }
      } else if (node.nodeType === Node.ELEMENT_NODE && (node.tagName === 'P' || node.tagName === 'DIV')) {
        // This is a much safer check for empty paragraphs. It checks if there's any visible text content or a <br> tag.
        if (!node.textContent.trim() && !node.querySelector('br')) {
          // This is an empty paragraph like <div> </div> or <p><b></b></p>, so we skip it.
          continue;
        }

        const children = parseNodeToTextRuns(node, {});
        paragraphs.push(new Paragraph({ children }));
      }
    }
  }

  // Ensure we have at least one paragraph
  if (paragraphs.length === 0) {
    paragraphs.push(new Paragraph({
      children: [new TextRun({ text: '' })]
    }));
  }

  const document = new Document({
    sections: [{ children: paragraphs }],
  });

  return Packer.toBlob(document);
}
