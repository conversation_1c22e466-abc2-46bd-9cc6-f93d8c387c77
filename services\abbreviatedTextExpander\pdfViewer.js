// pdfViewer.js - PDF viewer for Abbreviated Text Expander

import * as dom from '../../domElements.js';

let pdfDoc = null;
let currentPage = 1;
let totalPages = 1;
let scale = 1.0; // zoom multiplier relative to fit-to-width
let rendering = false;
let pendingPage = null;
const textCache = new Map(); // pageNum -> { items, viewportScale }
let pendingHighlight = null;

function getFitScale(page) {
  const viewportEl = (typeof dom.verificationPdfViewport !== 'undefined' && dom.verificationPdfViewport) ? dom.verificationPdfViewport : dom.abbrevPdfViewport;
  if (!viewportEl) return 1.0;
  const unscaled = page.getViewport({ scale: 1.0 });
  const padding = 16; // small padding inside viewport
  const availableWidth = Math.max(100, viewportEl.clientWidth - padding);
  return Math.max(0.5, Math.min(availableWidth / unscaled.width, 3));
}

function setFilename(name) {
  if (dom.abbrevPdfFilename) dom.abbrevPdfFilename.textContent = name || 'No PDF attached';
}

async function loadPdf(fileOrArrayBuffer) {
  try {
    const arrayBuffer = fileOrArrayBuffer instanceof ArrayBuffer
      ? fileOrArrayBuffer
      : await fileOrArrayBuffer.arrayBuffer();

    if (typeof pdfjsLib !== 'undefined') {
      pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
    }

    const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
    pdfDoc = await loadingTask.promise;
    totalPages = pdfDoc.numPages;
    currentPage = 1;
    if (dom.abbrevPdfTotalPages) dom.abbrevPdfTotalPages.textContent = String(totalPages);
    if (dom.abbrevPdfPageInput) dom.abbrevPdfPageInput.value = '1';
    // Hide empty placeholder
    const empty = document.getElementById('abbrev-pdf-empty');
    if (empty) empty.style.display = 'none';
    await renderPage(currentPage);
  } catch (err) {
    console.error('Failed to load PDF:', err);
  }
}

async function renderPage(pageNum) {
  const canvasEl = (typeof dom.verificationPdfCanvas !== 'undefined' && dom.verificationPdfCanvas) ? dom.verificationPdfCanvas : dom.abbrevPdfCanvas;
  if (!pdfDoc || !canvasEl) return;
  if (rendering) {
    pendingPage = pageNum;
    return;
  }
  rendering = true;
  const page = await pdfDoc.getPage(pageNum);
  const viewport = page.getViewport({ scale: getFitScale(page) * scale });
  const canvas = canvasEl;
  const ctx = canvas.getContext('2d');
  const dpr = window.devicePixelRatio || 1;
  // Size canvas in CSS pixels to fill container, and back buffer scaled by DPR for crispness
  canvas.style.width = `${viewport.width}px`;
  canvas.style.height = `${viewport.height}px`;
  canvas.width = Math.floor(viewport.width * dpr);
  canvas.height = Math.floor(viewport.height * dpr);
  ctx.setTransform(dpr, 0, 0, dpr, 0, 0);
  const renderContext = { canvasContext: ctx, viewport };
  await page.render(renderContext).promise;

  // Render textLayer for highlighting
  await renderTextLayer(page, viewport, pageNum);
  if (dom.abbrevPdfSearchIndicator) dom.abbrevPdfSearchIndicator.style.display = 'none';
  rendering = false;
  if (pendingPage !== null) {
    const next = pendingPage;
    pendingPage = null;
    await renderPage(next);
  }
}

async function renderTextLayer(page, viewport, pageNum) {
  const textLayerEl = (typeof dom.verificationPdfTextLayer !== 'undefined' && dom.verificationPdfTextLayer) ? dom.verificationPdfTextLayer : dom.abbrevPdfTextLayer;
  if (!textLayerEl) return;
  const textContent = await (textCache.get(pageNum)?.items ? Promise.resolve({ items: textCache.get(pageNum).items }) : page.getTextContent({ normalizeWhitespace: true }));
  if (!textCache.has(pageNum)) {
    textCache.set(pageNum, { items: textContent.items, viewportScale: 1 });
  } else {
    textCache.get(pageNum).viewportScale = 1;
  }

  const container = textLayerEl;
  container.innerHTML = '';
  container.style.width = `${viewport.width}px`;
  container.style.height = `${viewport.height}px`;

  const textItems = textContent.items;
  for (const item of textItems) {
    const span = document.createElement('span');
    span.textContent = item.str;
    const tx = pdfjsLib.Util.transform(viewport.transform, item.transform);
    const x = tx[4];
    const fontHeight = Math.hypot(tx[2], tx[3]);
    const y = tx[5] - fontHeight; // top-left
    const angle = Math.atan2(tx[1], tx[0]);
    span.style.left = `${x}px`;
    span.style.top = `${y}px`;
    span.style.fontSize = `${fontHeight}px`;
    span.style.transform = `rotate(${angle}rad)`;
    container.appendChild(span);
  }

  // Apply any pending highlight on this page
  if (pendingHighlight && pendingHighlight.page === pageNum) {
    highlightInTextLayer(pendingHighlight.normalizedQuery);
  }
}

function normalizeString(s) {
  return (s || '')
    .toLowerCase()
    .replace(/[\s\n\r]+/g, ' ')
    .replace(/[“”"'’`´]/g, '"')
    .replace(/[\-–—]/g, '-')
    .trim();
}

function highlightInTextLayer(normalizedQuery) {
  const textLayerEl = (typeof dom.verificationPdfTextLayer !== 'undefined' && dom.verificationPdfTextLayer) ? dom.verificationPdfTextLayer : dom.abbrevPdfTextLayer;
  if (!textLayerEl || !normalizedQuery) return;
  // Build a normalized text and map each normalized char back to its span
  const spans = Array.from(textLayerEl.querySelectorAll('span'));
  const indexToSpan = [];
  let normBuilder = '';

  function pushChar(ch, span) {
    normBuilder += ch;
    indexToSpan.push(span);
  }

  let lastWasSpace = false;
  for (const span of spans) {
    const text = (span.textContent || '');
    for (let i = 0; i < text.length; i++) {
      let ch = text[i];
      // Normalize
      if (/\s/.test(ch)) {
        if (!lastWasSpace) {
          pushChar(' ', span);
          lastWasSpace = true;
        }
        continue;
      }
      lastWasSpace = false;
      if (/[“”"'’`´]/.test(ch)) ch = '"';
      if (/[\-–—]/.test(ch)) ch = '-';
      pushChar(ch.toLowerCase(), span);
    }
    // add a word separator between spans
    if (!lastWasSpace) {
      pushChar(' ', span);
      lastWasSpace = true;
    }
  }

  const normText = normBuilder.trim();
  const idx = normText.indexOf(normalizedQuery);
  // Clear previous marks
  spans.forEach(s => s.classList.remove('match-highlight'));
  if (idx === -1) return;

  const endIdx = idx + normalizedQuery.length;
  const seen = new Set();
  for (let i = idx; i < endIdx && i < indexToSpan.length; i++) {
    const span = indexToSpan[i];
    if (!span || seen.has(span)) continue;
    seen.add(span);
    span.classList.add('match-highlight');
  }

  const first = indexToSpan[idx];
  if (first) {
    // Prefer smooth scroll within the dedicated viewport to avoid awkward page jumps
    const viewportEl = (typeof dom.verificationPdfViewport !== 'undefined' && dom.verificationPdfViewport) ? dom.verificationPdfViewport : dom.abbrevPdfViewport;
    if (viewportEl) {
      const spanTop = first.offsetTop;
      const desiredTop = Math.max(0, spanTop - Math.round(viewportEl.clientHeight * 0.35));
      try {
        viewportEl.scrollTo({ top: desiredTop, behavior: 'smooth' });
      } catch {
        viewportEl.scrollTop = desiredTop;
      }
    } else {
      first.scrollIntoView({ block: 'center', behavior: 'smooth' });
    }
  }
}

function changePage(delta) {
  if (!pdfDoc) return;
  const next = Math.max(1, Math.min(totalPages, currentPage + delta));
  if (next !== currentPage) {
    currentPage = next;
    if (dom.abbrevPdfPageInput) dom.abbrevPdfPageInput.value = String(currentPage);
    renderPage(currentPage);
  }
}

function setPage(num) {
  if (!pdfDoc) return;
  const next = Math.max(1, Math.min(totalPages, Number(num) || 1));
  currentPage = next;
  renderPage(currentPage);
}

function zoom(delta) {
  scale = Math.max(0.5, Math.min(3, scale + delta));
  renderPage(currentPage);
}

function resetZoom() {
  scale = 1.0;
  renderPage(currentPage);
}

export function initAbbrevPdfViewer() {
  // File input
  if (dom.abbrevPdfFileInput) {
    dom.abbrevPdfFileInput.addEventListener('change', async (e) => {
      const file = e.target.files?.[0];
      if (!file) return;
      setFilename(file.name);
      await loadPdf(file);
    });
  }

  // Buttons
  dom.abbrevPdfPrevBtn?.addEventListener('click', () => changePage(-1));
  dom.abbrevPdfNextBtn?.addEventListener('click', () => changePage(1));
  dom.abbrevPdfZoomInBtn?.addEventListener('click', () => zoom(0.2));
  dom.abbrevPdfZoomOutBtn?.addEventListener('click', () => zoom(-0.2));
  dom.abbrevPdfZoomResetBtn?.addEventListener('click', resetZoom);
  dom.abbrevPdfPageInput?.addEventListener('change', (e) => setPage(e.target.value));

  // If there is a logical place to auto-attach a matching PDF later, it can call loadPdf(arrayBuffer)

  // Resizable split behavior
  const split = document.getElementById('abbreviated-split');
  const resizer = document.getElementById('abbrev-split-resizer');
  if (split && resizer) {
    let dragging = false;
    const minLeft = 360; // px
    const minRight = 520; // px

    function setColumns(leftPx) {
      const total = split.clientWidth;
      const left = Math.max(minLeft, Math.min(total - minRight - 16, leftPx));
      const right = Math.max(minRight, total - left - resizer.offsetWidth - 16);
      split.style.gridTemplateColumns = `${left}px ${resizer.offsetWidth}px ${right}px`;
      // Persist as ratio for consistent restore across widths
      const ratio = left / (left + right);
      try { localStorage.setItem('abbrevSplitRatio', String(ratio)); } catch { /* ignore */ }
    }

    function onMouseMove(e) {
      if (!dragging) return;
      const rect = split.getBoundingClientRect();
      const left = e.clientX - rect.left - 8; // account for padding/gap
      setColumns(left);
    }

    function onMouseUp() {
      dragging = false;
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    }

    resizer.addEventListener('mousedown', (e) => {
      e.preventDefault();
      dragging = true;
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    });

    // Keyboard resizing (accessibility)
    resizer.addEventListener('keydown', (e) => {
      if (!['ArrowLeft', 'ArrowRight'].includes(e.key)) return;
      const rect = split.getBoundingClientRect();
      const computed = getComputedStyle(split).gridTemplateColumns.split(' ');
      const leftPx = parseFloat(computed[0]);
      const delta = e.key === 'ArrowLeft' ? -20 : 20;
      const nextLeft = isNaN(leftPx) ? (rect.width * 0.6) : (leftPx + delta);
      setColumns(nextLeft);
      e.preventDefault();
    });

    // Initialize from saved ratio or balanced split (50/50)
    requestAnimationFrame(() => {
      const rect = split.getBoundingClientRect();
      let ratio = 0.5;
      try {
        const saved = localStorage.getItem('abbrevSplitRatio');
        if (saved !== null) ratio = Math.min(0.8, Math.max(0.2, parseFloat(saved)));
      } catch { /* ignore */ }
      const usable = rect.width - resizer.offsetWidth - 16;
      setColumns(Math.round(usable * ratio));
    });
  }

  // Re-render on container resize to maintain fit-to-width
  const viewportEl = dom.abbrevPdfViewport;
  if (viewportEl) {
    const ro = new ResizeObserver(() => {
      clearTimeout(resizeDebounce);
      resizeDebounce = setTimeout(() => {
        // Re-apply saved split after container resize (e.g., on refresh or theme changes)
        const split = document.getElementById('abbreviated-split');
        const resizer = document.getElementById('abbrev-split-resizer');
        if (split && resizer) {
          const rect = split.getBoundingClientRect();
          const usable = rect.width - resizer.offsetWidth - 16;
          let ratio = 0.5;
          try { const saved = localStorage.getItem('abbrevSplitRatio'); if (saved !== null) ratio = Math.min(0.8, Math.max(0.2, parseFloat(saved))); } catch {}
          split.style.gridTemplateColumns = `${Math.round(usable * ratio)}px ${resizer.offsetWidth}px ${Math.round(usable * (1 - ratio))}px`;
        }
        renderPage(currentPage);
      }, 100);
    });
    ro.observe(viewportEl);
  }
}

export const AbbrevPdfAPI = {
  loadPdf,
  renderPage,
  setPage,
  zoom,
  resetZoom,
  syncPdfToSelection,
};

// Selection sync API
let searchDebounce = null;
export function syncPdfToSelection(selectedText) {
  if (!pdfDoc || !selectedText || selectedText.length < 5) return;
  const query = normalizeString(selectedText).slice(0, 200);
  clearTimeout(searchDebounce);
  if (dom.abbrevPdfSearchIndicator) {
    dom.abbrevPdfSearchIndicator.style.display = 'flex';
    dom.abbrevPdfSearchIndicator.classList.remove('status-success', 'status-warning');
    dom.abbrevPdfSearchIndicator.classList.add('status-loading');
    const textEl = dom.abbrevPdfSearchIndicator.querySelector('.text');
    if (textEl) textEl.textContent = 'Searching…';
  }
  searchDebounce = setTimeout(async () => {
    // Search pages in order: current, then outward
    const order = [];
    for (let i = 1; i <= totalPages; i++) order.push(i);
    order.sort((a, b) => Math.abs(a - currentPage) - Math.abs(b - currentPage));

    for (const p of order) {
  const page = await pdfDoc.getPage(p);
      const cached = textCache.get(p);
      const items = cached?.items || (await page.getTextContent()).items;
  if (!cached) textCache.set(p, { items, viewportScale: 1 });
      const normalized = normalizeString(items.map(it => it.str).join(' '));
      if (normalized.includes(query)) {
        pendingHighlight = { page: p, normalizedQuery: query };
        if (p !== currentPage) {
          currentPage = p;
          if (dom.abbrevPdfPageInput) dom.abbrevPdfPageInput.value = String(currentPage);
          await renderPage(currentPage);
        } else {
          highlightInTextLayer(query);
        }
        // If normalized search hit failed to highlight (due to segmentation), try fuzzy fallback
        if (dom.abbrevPdfTextLayer) {
          const anyMarked = dom.abbrevPdfTextLayer.querySelector('.match-highlight');
          if (!anyMarked) {
            // Heuristic: highlight spans in the middle of the page column where text density is highest
            const spans = Array.from(dom.abbrevPdfTextLayer.querySelectorAll('span'));
            const mid = Math.floor(spans.length / 2);
            for (let i = Math.max(0, mid - 3); i < Math.min(spans.length, mid + 3); i++) {
              spans[i].classList.add('match-highlight');
            }
          }
        }
        if (dom.abbrevPdfSearchIndicator) {
          dom.abbrevPdfSearchIndicator.style.display = 'flex';
          dom.abbrevPdfSearchIndicator.classList.remove('status-loading', 'status-warning');
          dom.abbrevPdfSearchIndicator.classList.add('status-success');
          const textEl = dom.abbrevPdfSearchIndicator.querySelector('.text');
          if (textEl) textEl.textContent = 'Match found';
          setTimeout(() => {
            if (dom.abbrevPdfSearchIndicator) dom.abbrevPdfSearchIndicator.style.display = 'none';
          }, 1200);
        }
        return;
      }
    }
    // Not found
    if (dom.abbrevPdfSearchIndicator) {
      dom.abbrevPdfSearchIndicator.style.display = 'flex';
      dom.abbrevPdfSearchIndicator.classList.remove('status-loading', 'status-success');
      dom.abbrevPdfSearchIndicator.classList.add('status-warning');
      const textEl = dom.abbrevPdfSearchIndicator.querySelector('.text');
      if (textEl) textEl.textContent = 'No match found';
      setTimeout(() => {
        if (dom.abbrevPdfSearchIndicator) dom.abbrevPdfSearchIndicator.style.display = 'none';
      }, 1500);
    }
  }, 250);
}


