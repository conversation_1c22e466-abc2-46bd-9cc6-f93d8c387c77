// index.js - Main Application Entry Point

// --- IMPORTS ---
import * as dom from './domElements.js';
import * as ui from './ui.js';
import * as state from './state.js';
import * as audioService from './services/audioService.js';
import * as fileService from './services/fileService.js';
import * as documentService from './services/documentService.js';
import * as ttsService from './services/ttsService.js';
import { initAiVerificationEventListeners } from './services/aiVerification.js';
import * as themeService from './services/themeService.js';
import { DEFAULT_G_TTS_VOICE_NAME, DEFAULT_MS_TTS_VOICE_NAME } from './constants.js';

// --- MAIN INITIALIZATION ---

/**
 * The primary function to start the application.
 */
async function initializeApp() {
    console.log("Starting application initialization...");

    // 1. Find all DOM elements
    dom.initDOMelements();

    // 2. Initialize themes
    await themeService.initThemes();
    ui.populateThemeSelector(themeService.getAvailableThemes(), 'default');
    themeService.applyTheme('default');

    // 3. Set up navigation and set the initial view
    dom.initNavigationEventListeners(); // This correctly centralizes navigation logic

    // 4. Connect modules by registering callbacks
    ui.registerUICallbacks({
        selectMusicTrack: audioService.selectMusicTrack,
        selectDocument: documentService.selectDocument,
        insertBreakTag: documentService.insertBreakTag,
    });

    // 5. Initialize all other event listeners
    initEventListeners();

    // 6. Populate UI elements that require async data
    fetchAndPopulateVoices();
    ui.renderBreakTagButtons();

    // 7. Set initial UI state
    ui.updateAudioControlsUI();



    console.log("Application Initialized Successfully.");
}

/**
 * A helper function to set up all event listeners in one place.
 */
function initEventListeners() {
    // Audio Player
    dom.audioPlayer.addEventListener('loadedmetadata', audioService.handleAudioLoadedMetadata);
    dom.audioPlayer.addEventListener('timeupdate', audioService.handleAudioTimeUpdate);
    dom.audioPlayer.addEventListener('ended', audioService.handleAudioEnded);
    dom.audioPlayer.addEventListener('play', () => {
        state.setIsPlaying(true);
        ui.updatePlayPauseButton();
    });
    dom.audioPlayer.addEventListener('pause', () => {
        state.setIsPlaying(false);
        ui.updatePlayPauseButton();
    });

    // Audio Controls
    dom.playPauseBtn.addEventListener('click', audioService.togglePlayPause);
    dom.nextTrackBtn.addEventListener('click', audioService.playNextTrack);
    dom.prevTrackBtn.addEventListener('click', audioService.playPrevTrack);
    dom.progressBar.addEventListener('input', audioService.handleSeek);
    dom.volumeSlider.addEventListener('input', audioService.handleVolumeChange);

    // File Inputs
    dom.musicFolderInput.addEventListener('change', (e) => fileService.handleFolderSelection(e, 'music'));
    dom.docFolderInput.addEventListener('change', (e) => fileService.handleFolderSelection(e, 'docs'));

    // Document Editor
    dom.toggleEditBtn.addEventListener('click', documentService.toggleEditMode);
    dom.saveDocBtn.addEventListener('click', documentService.saveDocument);
    dom.saveToFolderBtn.addEventListener('click', documentService.saveToOriginalFolder);
    dom.debugDocxBtn.addEventListener('click', documentService.debugCurrentDocx);

    // TTS Voice Selection
    dom.gTtsVoiceSelect.addEventListener('change', (e) => state.setSelectedGTTSVoiceName(e.target.value));
    dom.msTtsVoiceSelect.addEventListener('change', (e) => state.setSelectedMsTTSVoiceName(e.target.value));

    // Chirp3 HD Enhanced Narration toggles (global)
    if (dom.gTtsChirpEnhancedToggle) {
        dom.gTtsChirpEnhancedToggle.addEventListener('change', (e) => state.setEnhancedChirpNarrationEnabled(e.target.checked));
    }
    if (dom.aiVoiceChirpEnhancedToggle) {
        dom.aiVoiceChirpEnhancedToggle.addEventListener('change', (e) => state.setEnhancedChirpNarrationEnabled(e.target.checked));
    }

    // TTS Reprocessing Buttons
    dom.reprocessGTTSBtn.addEventListener('click', ttsService.handleReprocessWithGoogleTTS);
    dom.reprocessMsTTSBtn.addEventListener('click', ttsService.handleReprocessWithMicrosoftTTS);

    // TTS Save/Overwrite Button
    if (dom.saveTtsOverwriteBtn) {
        dom.saveTtsOverwriteBtn.addEventListener('click', ttsService.handleSaveTtsOverwrite);
    }

    // Voice Preview Buttons
    if (dom.previewGTTSVoiceBtn) {
        dom.previewGTTSVoiceBtn.addEventListener('click', ttsService.handlePreviewGoogleVoice);
    }
    if (dom.previewMsTTSVoiceBtn) {
        dom.previewMsTTSVoiceBtn.addEventListener('click', ttsService.handlePreviewMicrosoftVoice);
    }

    // Speaking Rate Sliders for Audiobook Verification tab
    if (dom.gTtsSpeedSlider && dom.gTtsSpeedValueDisplay) {
        dom.gTtsSpeedSlider.addEventListener('input', (e) => {
            dom.gTtsSpeedValueDisplay.textContent = parseFloat(e.target.value).toFixed(2);
        });
        dom.gTtsSpeedValueDisplay.textContent = parseFloat(dom.gTtsSpeedSlider.value).toFixed(2);
    }
    if (dom.msTtsSpeedSlider && dom.msTtsSpeedValueDisplay) {
        dom.msTtsSpeedSlider.addEventListener('input', (e) => {
            dom.msTtsSpeedValueDisplay.textContent = parseFloat(e.target.value).toFixed(2);
        });
        dom.msTtsSpeedValueDisplay.textContent = parseFloat(dom.msTtsSpeedSlider.value).toFixed(2);
    }

    // Theme Selection
    if (dom.themeSelect) {
        dom.themeSelect.addEventListener('change', (e) => {
            const themeName = e.target.value;
            themeService.applyTheme(themeName);
        });
    }

    // Initialize feature-specific listeners from other modules
    const uiHelpers = {
        updateStatus: ui.updateStatus,
        showSSMLModal: ui.showSSMLModal,
        hideSSMLModal: ui.hideSSMLModal,
        updateSSMLStatusBar: ui.updateSSMLStatusBar,
        populateGoogleVoiceSelector: ui.populateGoogleVoiceSelector,
        populateMicrosoftVoiceSelector: ui.populateMicrosoftVoiceSelector,
        renderVerificationResults: ui.renderVerificationResults,
        updateVerificationOverallStatus: ui.updateVerificationOverallStatus,
        updateAudioControlsUI: ui.updateAudioControlsUI,
    };
    initAiVerificationEventListeners(uiHelpers);
    // Defer heavy feature initializations until user opens the tabs
    // Whisper service: initialize on first modal open via button click
    if (dom.whisperAudioCheckBtn) {
        dom.whisperAudioCheckBtn.addEventListener('click', async () => {
            const { initWhisperService, showWhisperModal } = await import('./services/whisperService.js');
            initWhisperService(uiHelpers);
            showWhisperModal();
        });
    }

    // Quote & Footnote Inserter: lazy-init when tab button clicked
    if (dom.quoteFootnoteInserterBtn) {
        dom.quoteFootnoteInserterBtn.addEventListener('click', async () => {
            const { initQuoteFootnoteInserter } = await import('./services/quoteFootnoteInserter.js');
            initQuoteFootnoteInserter();
        });
    }

    // PDF to Text Converter: lazy-init when tab button clicked
    if (dom.pdfToTextConverterBtn) {
        dom.pdfToTextConverterBtn.addEventListener('click', async () => {
            const { initPdfToTextConverter } = await import('./services/pdfToTextConverter.js');
            initPdfToTextConverter();
        });
    }

    // AI Voice Creator: lazy-init when tab button clicked
    if (dom.aiVoiceCreatorBtn) {
        let aiVoiceCreatorInitialized = false;
        dom.aiVoiceCreatorBtn.addEventListener('click', async () => {
            try {
                const { initAiVoiceCreator } = await import('./services/aiVoiceCreator.js');
                if (!aiVoiceCreatorInitialized) {
                    initAiVoiceCreator(uiHelpers);
                    aiVoiceCreatorInitialized = true;
                    console.log('✅ AI Voice Creator initialized');
                }
            } catch (err) {
                console.error('Failed to initialize AI Voice Creator:', err);
                ui.updateStatus('Error initializing AI Voice Creator. Please refresh.');
            }
        });
    }

    // Abbreviated Text Expander: lazy-init when tab button clicked
    if (dom.abbreviatedTextExpanderBtn) {
        dom.abbreviatedTextExpanderBtn.addEventListener('click', async () => {
            const { initAbbreviatedTextExpander } = await import('./services/abbreviatedTextExpander.js');
            initAbbreviatedTextExpander();
        });
    }

        // SSML Editor: lazy-init when tab button clicked
        if (dom.audiobookTextEditorBtn) {
            let ssmlEditorInitialized = false;
            dom.audiobookTextEditorBtn.addEventListener('click', async () => {
                try {
                    const { initSsmEditorEventListeners } = await import('./services/ssmlEditor.js');
                    if (!ssmlEditorInitialized) {
                        initSsmEditorEventListeners(uiHelpers);
                        ssmlEditorInitialized = true;
                        console.log('✅ SSML Editor initialized');
                    }
                } catch (err) {
                    console.error('Failed to initialize SSML Editor:', err);
                    if (ui && typeof ui.updateSSMLStatusBar === 'function') {
                        ui.updateSSMLStatusBar('Error initializing SSML Editor. Please refresh.');
                    }
                }
            });
        }


    // Initialize playlist/doc split resizer behavior
    initPlaylistDocSplitResizer();

    // Start Verification button
    if (dom.startVerificationBtn) {
        dom.startVerificationBtn.addEventListener('click', async () => {
            const mod = await import('./services/aiVerification.js');
            await mod.handleStartVerification();
        });
    }

    // Toggle TTS controls visibility (left column) to create more space
    if (dom.toggleTtsControlsBtn) {
        dom.toggleTtsControlsBtn.addEventListener('click', () => {
            const ttsSection = document.querySelector('.tts-reprocessor-section');
            if (!ttsSection) return;
            const hidden = ttsSection.style.display === 'none';
            ttsSection.style.display = hidden ? '' : 'none';
            dom.toggleTtsControlsBtn.innerHTML = hidden
                ? '<span class="material-icons">tune</span> Hide TTS Controls'
                : '<span class="material-icons">tune</span> Show TTS Controls';
        });
    }

    // Open Reference PDF: auto-show verification panel and hide TTS controls
    if (dom.openVerificationPdfBtn) {
        dom.openVerificationPdfBtn.addEventListener('click', () => {
            // Ensure the verification process panel is visible
            if (dom.verificationProcessPanel) dom.verificationProcessPanel.style.display = 'flex';
            // Hide the TTS controls section to maximize space
            const ttsSection = document.querySelector('.tts-reprocessor-section');
            if (ttsSection) {
                ttsSection.style.display = 'none';
                if (dom.toggleTtsControlsBtn) dom.toggleTtsControlsBtn.innerHTML = '<span class="material-icons">tune</span> Show TTS Controls';
            }
            // Scroll the document area into view and open file picker
            const docPanel = document.querySelector('.document-panel');
            if (docPanel) docPanel.scrollIntoView({ behavior: 'smooth', block: 'start' });
            if (dom.verificationPdfFileInput) {
                try {
                    dom.verificationPdfFileInput.click();
                    ui.updateStatus('Select a reference PDF…');
                } catch (err) {
                    // Some browsers may block programmatic clicks; focus as fallback
                    dom.verificationPdfFileInput.focus();
                    dom.verificationPdfFileInput.style.outline = '2px solid var(--border-primary)';
                    ui.updateStatus('Click the PDF file input to select a file.');
                }
            } else {
                ui.updateStatus('PDF input not available. Try refreshing the page.');
            }
        });
    }
}

/**
 * Fetches TTS voice lists from services and populates the dropdowns.
 */
async function fetchAndPopulateVoices() {
    // Google Voices
    state.setAreGVoicesLoading(true);
    ui.populateGoogleVoiceSelector(dom.gTtsVoiceSelect, [], null);

    const gVoices = await ttsService.fetchAvailableGoogleVoices();
    state.setFetchedGTTSVoices(gVoices);
    state.setAreGVoicesLoading(false);
    ui.populateGoogleVoiceSelector(dom.gTtsVoiceSelect, gVoices, DEFAULT_G_TTS_VOICE_NAME);

    // Microsoft Voices
    state.setAreMsVoicesLoading(true);
    ui.populateMicrosoftVoiceSelector(dom.msTtsVoiceSelect, [], null);

    const msVoices = await ttsService.fetchAvailableMicrosoftVoices();
    state.setFetchedMsTTSVoices(msVoices);
    state.setAreMsVoicesLoading(false);
    ui.populateMicrosoftVoiceSelector(dom.msTtsVoiceSelect, msVoices, DEFAULT_MS_TTS_VOICE_NAME);

    // Populate AI Voice Creator dropdown based on currently selected provider
    if (dom.aiVoiceTTSVoiceSelect && dom.aiVoiceTTSProviderSelect) {
        const currentProvider = dom.aiVoiceTTSProviderSelect.value;
        if (currentProvider === 'google') {
            ui.populateGoogleVoiceSelector(dom.aiVoiceTTSVoiceSelect, gVoices, DEFAULT_G_TTS_VOICE_NAME);
        } else if (currentProvider === 'microsoft') {
            ui.populateMicrosoftVoiceSelector(dom.aiVoiceTTSVoiceSelect, msVoices, DEFAULT_MS_TTS_VOICE_NAME);
        }
    }

    ui.updateAudioControlsUI();
}

// --- START THE APPLICATION ---
document.addEventListener('DOMContentLoaded', initializeApp);

// --- Playlist/Document Split Resizer ---
let playlistDocResizeDebounce = null;
function initPlaylistDocSplitResizer() {
    const split = document.getElementById('playlist-doc-split');
    const resizer = document.getElementById('playlist-doc-resizer');
    if (!split || !resizer) return;

    let dragging = false;
    const minLeft = 240;  // px, playlist min width
    const minRight = 360; // px, documents min width

    function setColumns(leftPx) {
        const total = split.clientWidth;
        const left = Math.max(minLeft, Math.min(total - minRight - resizer.offsetWidth - 15, leftPx));
        const right = Math.max(minRight, total - left - resizer.offsetWidth - 15);
        split.style.gridTemplateColumns = `${left}px ${resizer.offsetWidth}px ${right}px`;
        const ratio = left / (left + right);
        try { localStorage.setItem('playlistDocSplitRatio', String(ratio)); } catch {}
        // After column change, re-render verification PDF if present to fit width
        if (dom.verificationPdfCanvas) {
            clearTimeout(playlistDocResizeDebounce);
            playlistDocResizeDebounce = setTimeout(async () => {
                try {
                    const mod = await import('./services/abbreviatedTextExpander/pdfViewer.js');
                    if (mod && mod.AbbrevPdfAPI && typeof mod.AbbrevPdfAPI.setPage === 'function') {
                        const current = Number(dom.verificationPdfPageInput?.value) || 1;
                        mod.AbbrevPdfAPI.setPage(current);
                    }
                } catch {}
            }, 80);
        }
    }

    function onMouseMove(e) {
        if (!dragging) return;
        const rect = split.getBoundingClientRect();
        const left = e.clientX - rect.left - 8; // account for gap/padding
        setColumns(left);
    }

    function onMouseUp() {
        dragging = false;
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
    }

    resizer.addEventListener('mousedown', (e) => {
        e.preventDefault();
        dragging = true;
        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
    });

    // Keyboard accessibility
    resizer.addEventListener('keydown', (e) => {
        if (!['ArrowLeft', 'ArrowRight'].includes(e.key)) return;
        const rect = split.getBoundingClientRect();
        const computed = getComputedStyle(split).gridTemplateColumns.split(' ');
        const leftPx = parseFloat(computed[0]);
        const delta = e.key === 'ArrowLeft' ? -20 : 20;
        const nextLeft = isNaN(leftPx) ? (rect.width * 0.4) : (leftPx + delta);
        setColumns(nextLeft);
        e.preventDefault();
    });

    // Initialize from saved ratio or default 40/60 bias to documents
    requestAnimationFrame(() => {
        const rect = split.getBoundingClientRect();
        let ratio = 0.4;
        try {
            const saved = localStorage.getItem('playlistDocSplitRatio');
            if (saved !== null) ratio = Math.min(0.8, Math.max(0.2, parseFloat(saved)));
        } catch {}
        const usable = rect.width - resizer.offsetWidth - 15;
        setColumns(Math.round(usable * ratio));
    });

    // Adjust on resize to keep ratio
    const ro = new ResizeObserver(() => {
        clearTimeout(playlistDocResizeDebounce);
        playlistDocResizeDebounce = setTimeout(() => {
            const rect = split.getBoundingClientRect();
            const usable = rect.width - resizer.offsetWidth - 15;
            let ratio = 0.4;
            try { const saved = localStorage.getItem('playlistDocSplitRatio'); if (saved !== null) ratio = Math.min(0.8, Math.max(0.2, parseFloat(saved))); } catch {}
            split.style.gridTemplateColumns = `${Math.round(usable * ratio)}px ${resizer.offsetWidth}px ${Math.round(usable * (1 - ratio))}px`;
        }, 100);
    });
    ro.observe(split);
}