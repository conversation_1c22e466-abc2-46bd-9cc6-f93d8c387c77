/* pdfRasterWorker.js - classic worker for rasterizing PDF pages with pdf.js + OffscreenCanvas */

/* Load pdf.js in worker context */
self.importScripts('https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js');

let pdfDocument = null;

self.onmessage = async (evt) => {
  const msg = evt.data || {};
  try {
    if (msg.type === 'init') {
      const { pdfBuffer } = msg;
      if (!pdfBuffer) throw new Error('Missing pdfBuffer');
      const loadingTask = self.pdfjsLib.getDocument({ data: pdfBuffer });
      pdfDocument = await loadingTask.promise;
      self.postMessage({ type: 'ready', numPages: pdfDocument.numPages });
      return;
    }

    if (msg.type === 'render') {
      if (!pdfDocument) throw new Error('PDF not initialized');
      const { requestId, pageNum, dpi = 300, format = 'jpeg', quality = 0.9 } = msg;
      const page = await pdfDocument.getPage(pageNum);
      const viewport = page.getViewport({ scale: dpi / 72 });

      // Prefer OffscreenCanvas; fallback to simple canvas-like via OffscreenCanvas polyfill is not available here
      const canvas = new OffscreenCanvas(Math.ceil(viewport.width), Math.ceil(viewport.height));
      const ctx = canvas.getContext('2d');
      await page.render({ canvasContext: ctx, viewport }).promise;

      const mime = `image/${String(format).toLowerCase()}`;
      const q = String(format).toLowerCase() === 'png' ? undefined : Math.max(0.1, Math.min(1, quality || 0.9));
      const blob = await canvas.convertToBlob({ type: mime, quality: q });
      const buffer = await blob.arrayBuffer();
      self.postMessage({ type: 'rendered', requestId, pageNum, mime, buffer }, [buffer]);
      return;
    }
  } catch (err) {
    const payload = { type: 'error', message: err?.message || String(err) };
    if (evt.data && evt.data.requestId) payload.requestId = evt.data.requestId;
    self.postMessage(payload);
  }
};

