/********************************************************************
 *  ttsService.js
 *  ---------------------------------------------------------------
 *  Google Cloud Text-to-Speech  +  Microsoft Azure TTS (live REST)
 *******************************************************************/

import * as dom   from '../domElements.js';
import * as state from '../state.js';
import * as ui    from '../ui.js';

import {
  /* GOOGLE CLOUD TTS */
  G_TTS_API_KEY,
  DEFAULT_G_TTS_VOICE_NAME,
  EXTENSION_TO_ENCODING_GOOGLE,
  G_TTS_SPEECH_SPEED,
  G_TTS_SAMPLE_RATE_HERTZ,

  /* MICROSOFT */
  MS_TTS_API_KEY,
  DEFAULT_MS_TTS_VOICE_NAME,
  MS_TTS_SPEECH_SPEED,
  MS_TTS_SAMPLE_RATE_HERTZ,
  MS_TTS_SERVICE_REGION,
} from '../constants.js';

import {
  base64ToBlob,
  naturalSort,
  ssmlEscapeXmlEntities,
} from '../utils.js';

import { selectMusicTrack } from './audioService.js';
import { refreshHighlighting } from './highlightService.js';

/*───────────────────────────────────────────────────────────────────
  Shared helper – update playlist & UI after synthesis
───────────────────────────────────────────────────────────────────*/
function updateMusicFileListWithNewTTS(entry, provider, verificationCtx = false) {
  console.log(`updateMusicFileListWithNewTTS called for ${provider}:`, entry.name);

  if (verificationCtx) {
    console.log(`${provider} audio "${entry.name}" created for verification.`);
    return;
  }

  const base = entry.name.replace(/^\((G-TTS|MS-TTS)\)\s*/, '');
  console.log('Looking for existing TTS file with base name:', base);

  const oldIdx = state.musicFiles.findIndex(
    f => f.isSynthesized && f.name.replace(/^\((G-TTS|MS-TTS)\)\s*/, '') === base,
  );
  console.log('Found existing TTS file at index:', oldIdx);

  if (oldIdx !== -1) {
    const old = state.musicFiles[oldIdx];
    console.log('Removing old TTS file:', old.name);
    if (old.objectURL && old.objectURL !== entry.objectURL) URL.revokeObjectURL(old.objectURL);
    state.musicFiles.splice(oldIdx, 1);
  }

  state.musicFiles.push(entry);
  state.musicFiles.sort((a, b) => naturalSort(a.name, b.name));

  ui.renderMusicPlaylist();
  ui.updateStatus(`${provider} audio "${entry.name}" created and added to playlist.`);
  selectMusicTrack(entry.id);

  // Refresh highlighting after new TTS audio is selected
  setTimeout(() => refreshHighlighting(), 200);
}

/*───────────────────────────────────────────────────────────────────
  Voice Preview — generate and play a short sample without playlist
───────────────────────────────────────────────────────────────────*/
async function playPreviewAudioFromBlob(audioBlob) {
  try {
    const url = URL.createObjectURL(audioBlob);
    const audio = new Audio(url);
    audio.onended = () => URL.revokeObjectURL(url);
    await audio.play();
  } catch (err) {
    console.error('Error playing preview audio:', err);
  }
}

export async function handlePreviewGoogleVoice() {
  if (!G_TTS_API_KEY) {
    ui.updateStatus('Cannot preview Google TTS: API key not configured.');
    return;
  }
  if (state.areGVoicesLoading) {
    ui.updateStatus('Cannot preview Google TTS: voices are still loading.');
    return;
  }
  const voice = state.fetchedGTTSVoices.find(v => v.name === state.selectedGTTSVoiceName);
  if (!voice) {
    ui.updateStatus('Selected Google TTS voice is invalid.');
    return;
  }
  try {
    // Prefer plain text to avoid SSML-incompatible voices
    const text = 'Hello, this is a preview of the voice.';
    const speakingRate = dom.gTtsSpeedSlider ? parseFloat(dom.gTtsSpeedSlider.value) : G_TTS_SPEECH_SPEED;
    const { audioBlob } = await synthesizeWithGoogleTTSText(text, voice.name, voice.languageCode, 'MP3', G_TTS_SAMPLE_RATE_HERTZ, speakingRate);
    await playPreviewAudioFromBlob(audioBlob);
  } catch (err) {
    console.error('Google TTS preview error:', err);
    ui.updateStatus(`Error during Google TTS preview: ${err.message}`);
  }
}

export async function handlePreviewMicrosoftVoice() {
  if (!MS_TTS_API_KEY || !MS_TTS_SERVICE_REGION) {
    ui.updateStatus('Cannot preview Microsoft TTS: API key or region not configured.');
    return;
  }
  if (state.areMsVoicesLoading) {
    ui.updateStatus('Cannot preview Microsoft TTS: voices are still loading.');
    return;
  }
  const voice = state.fetchedMsTTSVoices.find(v => v.name === state.selectedMsTTSVoiceName);
  if (!voice) {
    ui.updateStatus('Selected Microsoft TTS voice is invalid.');
    return;
  }
  try {
    const text = 'Hello, this is a preview of the voice.';
    const sliderValue = dom.msTtsSpeedSlider ? parseFloat(dom.msTtsSpeedSlider.value) : 1.0;
    const speakingRate = typeof sliderValue === 'number' ? `${Math.round(sliderValue * 100)}%` : MS_TTS_SPEECH_SPEED;
    const { audioBlob } = await synthesizeWithMicrosoftTTS(text, voice.name, voice.languageCode, speakingRate);
    await playPreviewAudioFromBlob(audioBlob);
  } catch (err) {
    console.error('Microsoft TTS preview error:', err);
    ui.updateStatus(`Error during Microsoft TTS preview: ${err.message}`);
  }
}

/*===================================================================
  GOOGLE CLOUD TTS
===================================================================*/

/** 1️⃣ Fetch Google Cloud TTS voice catalogue */
export async function fetchAvailableGoogleVoices() {
  console.log('fetchAvailableGoogleVoices: Starting...');
  console.log('fetchAvailableGoogleVoices: G_TTS_API_KEY available:', !!G_TTS_API_KEY);
  console.log('fetchAvailableGoogleVoices: G_TTS_API_KEY length:', G_TTS_API_KEY?.length || 0);

  if (!G_TTS_API_KEY) {
    console.log('fetchAvailableGoogleVoices: No API key, returning empty array');
    return [];
  }

  state.setAreGVoicesLoading(true);
  ui.updateStatus('Fetching Google Cloud TTS voices…');
  dom.gTtsVoiceSelect.innerHTML = '<option value="">Loading Google voices…</option>';
  ui.updateAudioControlsUI();

  try {
    // Use Google Cloud Text-to-Speech REST API
    const url = `https://texttospeech.googleapis.com/v1/voices?key=${G_TTS_API_KEY}`;
    console.log('fetchAvailableGoogleVoices: Making request to:', url.replace(G_TTS_API_KEY, 'HIDDEN_KEY'));

    const res = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      }
    });
    console.log('fetchAvailableGoogleVoices: Response status:', res.status);
    console.log('fetchAvailableGoogleVoices: Response ok:', res.ok);

    if (!res.ok) {
      const data = await res.json();
      console.log('fetchAvailableGoogleVoices: Error response:', data);
      throw new Error(data.error?.message || res.statusText);
    }

    const responseData = await res.json();
    console.log('fetchAvailableGoogleVoices: Response data:', responseData);

    const { voices: raw } = responseData;
    console.log('fetchAvailableGoogleVoices: Raw voices count:', raw?.length || 0);

    const voices = raw
      .map(v => ({
        name: v.name,
        label: `${v.name} (${v.languageCodes[0]}${v.ssmlGender ? ' - ' + v.ssmlGender : ''})`,
        languageCode: v.languageCodes[0],
        ssmlGender: v.ssmlGender,
      }))
      .sort((a, b) => a.label.localeCompare(b.label));

    console.log('fetchAvailableGoogleVoices: Processed voices count:', voices.length);
    state.setFetchedGTTSVoices(voices);
    dom.gTtsVoiceSelect.innerHTML = '';

    if (voices.length === 0) {
      console.log('fetchAvailableGoogleVoices: No voices found, showing error message');
      dom.gTtsVoiceSelect.innerHTML = '<option value="">No Google voices found</option>';
    } else {
      voices.forEach(v => {
        const opt = document.createElement('option');
        opt.value = v.name;
        opt.textContent = v.label;
        dom.gTtsVoiceSelect.appendChild(opt);
      });

      const def = voices.find(v => v.name === DEFAULT_G_TTS_VOICE_NAME) || voices[0];
      state.setSelectedGTTSVoiceName(def.name);
      dom.gTtsVoiceSelect.value = def.name;
    }

    ui.updateStatus('Google Cloud TTS voices loaded.');
    return voices;
  } catch (err) {
    console.error('Error fetching Google Cloud TTS voices:', err);
    console.error('Error type:', err.constructor.name);
    console.error('Error message:', err.message);
    console.error('Error stack:', err.stack);

    // Check if it's a CORS error
    if (err.message.includes('CORS') || err.message.includes('cross-origin') || err.name === 'TypeError') {
      console.error('This appears to be a CORS error - Google Cloud TTS API may not support direct browser requests');
      dom.gTtsVoiceSelect.innerHTML = '<option value="">CORS Error: API not accessible from browser</option>';
      ui.updateStatus('Error: Google Cloud TTS API blocked by CORS policy. Consider using a server-side proxy.');
    } else {
      dom.gTtsVoiceSelect.innerHTML = '<option value="">Error loading Google voices</option>';
      ui.updateStatus(`Error loading Google voices: ${err.message}`);
    }

    state.setFetchedGTTSVoices([]);
    return [];
  } finally {
    state.setAreGVoicesLoading(false);
    ui.updateAudioControlsUI();
  }
}


// Helper: strip SSML/XML tags to plain text (basic, browser-safe)
function stripSSMLToPlainText(src) {
  if (typeof src !== 'string') return '';
  // Remove tags, collapse whitespace
  const noTags = src.replace(/<[^>]+>/g, ' ');
  const collapsed = noTags.replace(/\s+/g, ' ').trim();
  // Decode HTML entities via textarea trick (browser environment)
  try {
    const ta = document.createElement('textarea');
    ta.innerHTML = collapsed;
    return ta.value;
  } catch {
    return collapsed;
  }
}


// Detect Chirp3 HD-capable Google voices by name
function isChirp3HDVoiceName(name) {
  if (!name) return false;
  const n = String(name).toLowerCase();
  // Matches: "Chirp3", "Chirp-3", or "Chirp 3"; "hd" hint optional
  return /(chirp\s*-?\s*3)/i.test(name) || (n.includes('chirp') && n.includes('hd'));
}

/** 2️⃣ Low-level Google Cloud TTS synth */
async function synthesizeWithGoogleTTS(
  ssml,
  voiceName,
  langCode,
  audioEncoding = 'MP3',
  sampleRate = G_TTS_SAMPLE_RATE_HERTZ,
  speakingRate = undefined,
) {
  if (!G_TTS_API_KEY) throw new Error('Google Cloud TTS API key missing.');

  const audioConfig = { audioEncoding, sampleRateHertz: sampleRate };
  if (typeof speakingRate === 'number') audioConfig.speakingRate = speakingRate;

  const res = await fetch(
    `https://texttospeech.googleapis.com/v1/text:synthesize?key=${G_TTS_API_KEY}`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        input: { ssml },
        voice: { languageCode: langCode, name: voiceName },
        audioConfig,
      }),
    },
  );

  if (!res.ok) {
    const data = await res.json();
    throw new Error(data.error?.message || res.statusText);
  }

  const { audioContent } = await res.json();
  const mime =
    audioEncoding === 'LINEAR16'
      ? 'audio/wav'
      : audioEncoding === 'OGG_OPUS'
      ? 'audio/ogg'
      : audioEncoding === 'FLAC'
      ? 'audio/flac'
      : 'audio/mpeg';

  return { audioBlob: base64ToBlob(audioContent, mime), mimeType: mime };
}

/** 2️⃣a Google Cloud TTS synth (plain text input). Some voices do not accept SSML */
async function synthesizeWithGoogleTTSText(
  text,
  voiceName,
  langCode,
  audioEncoding = 'MP3',
  sampleRate = G_TTS_SAMPLE_RATE_HERTZ,
  speakingRate = undefined,
  useMarkup = false,
) {
  if (!G_TTS_API_KEY) throw new Error('Google Cloud TTS API key missing.');

  const input = useMarkup ? { markup: text } : { text };
  const audioConfig = { audioEncoding, sampleRateHertz: sampleRate };
  if (typeof speakingRate === 'number') audioConfig.speakingRate = speakingRate;

  const res = await fetch(
    `https://texttospeech.googleapis.com/v1/text:synthesize?key=${G_TTS_API_KEY}`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        input,
        voice: { languageCode: langCode, name: voiceName },
        audioConfig,
      }),
    },
  );

  if (!res.ok) {
    const data = await res.json();
    throw new Error(data.error?.message || res.statusText);
  }

  const { audioContent } = await res.json();
  const mime =
    audioEncoding === 'LINEAR16'
      ? 'audio/wav'
      : audioEncoding === 'OGG_OPUS'
      ? 'audio/ogg'
      : audioEncoding === 'FLAC'
      ? 'audio/flac'
      : 'audio/mpeg';

  return { audioBlob: base64ToBlob(audioContent, mime), mimeType: mime };
}

/** 3️⃣ Re-process current doc with Google TTS (UI button) */
export async function handleReprocessWithGoogleTTS() {
  const reason = ui.getGTTSDisabledReason();
  if (reason) {
    ui.updateStatus(`Cannot reprocess with Google TTS: ${reason}`);
    return;
  }

  const voice = state.fetchedGTTSVoices.find(v => v.name === state.selectedGTTSVoiceName);
  if (!voice) {
    ui.updateStatus('Selected Google TTS voice is invalid.');
    return;
  }

  state.setIsGTTSreprocessing(true);
  ui.updateStatus(`Reprocessing "${state.currentDocumentFile.name}" with Google TTS (${voice.label})…`);
  ui.updateAudioControlsUI();

  try {
    let text = '';
    if (state.isDocEditing) {
      text = dom.docEditorTextarea.value;
    } else {
      // Handle both txt (pre element) and docx (HTML content) files
      if (state.currentDocumentFile.type === 'txt') {
        text = dom.docHtmlViewer.querySelector('pre')?.textContent || '';
      } else if (state.currentDocumentFile.type === 'docx') {
        // For DOCX files, use the stored plain text content if available
        // This preserves SSML markup better than extracting from HTML
        if (state.currentDocumentFile.plainTextContent) {
          text = state.currentDocumentFile.plainTextContent;
        } else {
          // Fallback to extracting from HTML content
          text = dom.docHtmlViewer.textContent || dom.docHtmlViewer.innerText || '';
        }
      }
    }

    const ext = state.currentAudioFile.name.slice(state.currentAudioFile.name.lastIndexOf('.')).toLowerCase();
    const enc = EXTENSION_TO_ENCODING_GOOGLE[ext] || 'MP3';

    let audioBlob, mimeType;
    const isChirp = isChirp3HDVoiceName(voice.name);

    if (isChirp) {
      // Chirp 3 HD does not support SSML and reads raw text; do NOT prepend narration prompt.
      const plain = /^<speak\b/i.test(text.trim()) ? stripSSMLToPlainText(text) : text;
      const speakingRate = dom.gTtsSpeedSlider ? parseFloat(dom.gTtsSpeedSlider.value) : G_TTS_SPEECH_SPEED;
      ({ audioBlob, mimeType } = await synthesizeWithGoogleTTSText(
        plain,
        voice.name,
        voice.languageCode,
        enc,
        G_TTS_SAMPLE_RATE_HERTZ,
        speakingRate,
        false,
      ));
    } else {
      // Build SSML and synthesize; fall back to text for SSML-incompatible voices
      const speakingRate = dom.gTtsSpeedSlider ? parseFloat(dom.gTtsSpeedSlider.value) : G_TTS_SPEECH_SPEED;
      const ssml = /^<speak\b/i.test(text.trim())
        ? text
        : `<speak><prosody rate="${speakingRate}">${ssmlEscapeXmlEntities(text)}</prosody></speak>`;
      try {
        // Only pass speakingRate to API if SSML doesn't already contain prosody rate
        const hasProsodyRate = /<prosody[^>]*rate=/.test(ssml);
        ({ audioBlob, mimeType } = await synthesizeWithGoogleTTS(
          ssml,
          voice.name,
          voice.languageCode,
          enc,
          G_TTS_SAMPLE_RATE_HERTZ,
          hasProsodyRate ? undefined : speakingRate,
        ));
      } catch (e) {
        const msg = (e && e.message ? e.message : '').toLowerCase();
        if (msg.includes('ssml')) {
          const plain2 = /^<speak\b/i.test(text.trim()) ? stripSSMLToPlainText(text) : text;
          ({ audioBlob, mimeType } = await synthesizeWithGoogleTTSText(
            plain2,
            voice.name,
            voice.languageCode,
            enc,
            G_TTS_SAMPLE_RATE_HERTZ,
            speakingRate,
          ));
        } else {
          throw e;
        }
      }
    }

    const base = state.currentAudioFile.name.replace(/^\((G-TTS|MS-TTS)\)\s*/, '').replace(ext, '');
    const fileName = `(G-TTS) ${base}${ext}`;
    const url = URL.createObjectURL(audioBlob);

    const entry = {
      id: `${fileName}_${Date.now()}`,
      file: new File([audioBlob], fileName, { type: mimeType }),
      name: fileName,
      duration: 0,
      objectURL: url,
      isSynthesized: true,
      ttsProvider: 'google',
    };

    const tmp = new Audio(url);
    tmp.onloadedmetadata = async () => {
      entry.duration = tmp.duration;

      // Store original audio info BEFORE any playlist changes
      const originalAudioId = state.currentAudioFile.id;
      const originalAudioFile = state.currentAudioFile;
      const ext  = originalAudioFile.name.slice(originalAudioFile.name.lastIndexOf('.')).toLowerCase();
      const suggestedName = originalAudioFile.name.replace(/^[^a-zA-Z0-9(]+/, '');

      console.log('Google TTS: Storing original audio ID:', originalAudioId, 'for file:', originalAudioFile.name);

      // Auto-open save picker for the newly generated audio
      ui.updateStatus('Google TTS finished. Choose where to save the new audio…');
      await saveAudioBlobAuto(entry.file, suggestedName, mimeType);

      // Also update the in-app playlist entry so it plays the TTS result immediately
      const updated = await replacePlaylistEntryWithBlob(originalAudioFile, entry.file, mimeType);
      state.setLatestTtsAudioForSave({ audioEntry: updated, originalAudioId, originalAudioFile, provider: 'google' });
      ui.updateAudioControlsUI();

      // Ensure current player points to the updated entry
      const wasPlaying = state.isPlaying;
      await selectMusicTrack(updated.id, wasPlaying);
      ui.updateStatus(`Reprocessed with Google TTS and saved. Now playing updated audio.`);
    };
    tmp.onerror = async () => {
      // Store original audio info BEFORE any playlist changes
      const originalAudioId = state.currentAudioFile.id;
      const originalAudioFile = state.currentAudioFile;
      const ext  = originalAudioFile.name.slice(originalAudioFile.name.lastIndexOf('.')).toLowerCase();
      const suggestedName = originalAudioFile.name.replace(/^[^a-zA-Z0-9(]+/, '');

      // Auto-open save picker as fallback path as well
      ui.updateStatus('Google TTS finished (metadata load error). Choose where to save the new audio…');
      await saveAudioBlobAuto(entry.file, suggestedName, mimeType);

      // Update in-app playlist
      const updated = await replacePlaylistEntryWithBlob(originalAudioFile, entry.file, mimeType);
      state.setLatestTtsAudioForSave({ audioEntry: updated, originalAudioId, originalAudioFile, provider: 'google' });
      ui.updateAudioControlsUI();

      const wasPlaying = state.isPlaying;
      await selectMusicTrack(updated.id, wasPlaying);
      ui.updateStatus(`Reprocessed with Google TTS and saved. Now playing updated audio.`);
    };
  } catch (err) {
    console.error('Google TTS error:', err);
    ui.updateStatus(`Error during Google TTS reprocessing: ${err.message}`);
  } finally {
    state.setIsGTTSreprocessing(false);
    ui.updateAudioControlsUI();
  }
}

/*===================================================================
  MICROSOFT AZURE – LIVE REST IMPLEMENTATION
===================================================================*/

/** 1️⃣ Fetch Azure voice catalogue */
export async function fetchAvailableMicrosoftVoices() {
  console.log('fetchAvailableMicrosoftVoices: Starting...');
  console.log('fetchAvailableMicrosoftVoices: MS_TTS_API_KEY available:', !!MS_TTS_API_KEY);
  console.log('fetchAvailableMicrosoftVoices: MS_TTS_API_KEY length:', MS_TTS_API_KEY?.length || 0);
  console.log('fetchAvailableMicrosoftVoices: MS_TTS_SERVICE_REGION available:', !!MS_TTS_SERVICE_REGION);
  console.log('fetchAvailableMicrosoftVoices: MS_TTS_SERVICE_REGION value:', MS_TTS_SERVICE_REGION);

  if (!MS_TTS_API_KEY || !MS_TTS_SERVICE_REGION) {
    console.log('fetchAvailableMicrosoftVoices: Missing API key or region, returning empty array');
    state.setAreMsVoicesLoading(false);
    dom.msTtsVoiceSelect.innerHTML =
      '<option value="">MS TTS N/A: Key or Region Missing</option>';
    dom.msTtsVoiceSelect.disabled = true;
    dom.reprocessMsTTSBtn.disabled = true;
    ui.updateAudioControlsUI();
    return [];
  }

  state.setAreMsVoicesLoading(true);
  ui.updateStatus('Fetching Microsoft TTS voices…');
  dom.msTtsVoiceSelect.innerHTML = '<option value="">Loading MS voices…</option>';
  ui.updateAudioControlsUI();

  try {
    const res = await fetch(
      `https://${MS_TTS_SERVICE_REGION}.tts.speech.microsoft.com/cognitiveservices/voices/list`,
      { headers: { 'Ocp-Apim-Subscription-Key': MS_TTS_API_KEY } },
    );
    if (!res.ok) {
      const txt = await res.text();
      throw new Error(`Azure voice list failed (${res.status}): ${txt || res.statusText}`);
    }

    const raw = await res.json();
    const voices = raw
      .map(v => ({
        name: v.ShortName,
        label: `${v.ShortName} (${v.Locale}${v.Gender ? ' - ' + v.Gender : ''})`,
        languageCode: v.Locale,
        ssmlGender: v.Gender,
      }))
      .sort((a, b) => a.label.localeCompare(b.label));

    state.setFetchedMsTTSVoices(voices);
    dom.msTtsVoiceSelect.innerHTML = '';

    if (voices.length === 0) {
      dom.msTtsVoiceSelect.innerHTML = '<option value="">No MS voices found</option>';
    } else {
      voices.forEach(v => {
        const opt = document.createElement('option');
        opt.value = v.name;
        opt.textContent = v.label;
        dom.msTtsVoiceSelect.appendChild(opt);
      });

      const def = voices.find(v => v.name === DEFAULT_MS_TTS_VOICE_NAME) || voices[0];
      state.setSelectedMsTTSVoiceName(def.name);
      dom.msTtsVoiceSelect.value = def.name;
    }

    ui.updateStatus('Microsoft TTS voices loaded.');
    return voices;
  } catch (err) {
    console.error('Error fetching MS voices:', err);
    dom.msTtsVoiceSelect.innerHTML = '<option value="">Error loading MS voices</option>';
    state.setFetchedMsTTSVoices([]);
    ui.updateStatus(`Error loading MS voices: ${err.message}`);
    return [];
  } finally {
    state.setAreMsVoicesLoading(false);
    ui.updateAudioControlsUI();
  }
}

/** 2️⃣ Low-level Azure synth */
async function synthesizeWithMicrosoftTTS(ssml, voiceName, langCode, speakingRate = MS_TTS_SPEECH_SPEED) {
  if (!MS_TTS_API_KEY || !MS_TTS_SERVICE_REGION) {
    throw new Error('Microsoft TTS API key or region missing.');
  }

  const endpoint = `https://${MS_TTS_SERVICE_REGION}.tts.speech.microsoft.com/cognitiveservices/v1`;

  const wrappedSSML = /^<speak\b/i.test(ssml.trim())
    ? ssml
    : `<speak version='1.0' xml:lang='${langCode}'>
         <voice name='${voiceName}'>
           <prosody rate='${speakingRate}'>${ssmlEscapeXmlEntities(ssml)}</prosody>
         </voice>
       </speak>`;

  const res = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Ocp-Apim-Subscription-Key': MS_TTS_API_KEY,
      'Content-Type': 'application/ssml+xml',
      'X-Microsoft-OutputFormat': MS_TTS_SAMPLE_RATE_HERTZ,
    },
    body: wrappedSSML,
  });

  if (!res.ok) {
    const txt = await res.text();
    throw new Error(`Azure TTS failed (${res.status}): ${txt || res.statusText}`);
  }

  const buf = await res.arrayBuffer();
  return { audioBlob: new Blob([buf], { type: 'audio/mpeg' }), mimeType: 'audio/mpeg' };
}

/** 3️⃣ Re-process current doc with Azure TTS (UI button) */
export async function handleReprocessWithMicrosoftTTS() {
  const reason = ui.getMsTTSDisabledReason();
  if (reason) {
    ui.updateStatus(`Cannot reprocess with Microsoft TTS: ${reason}`);
    return;
  }

  const voice = state.fetchedMsTTSVoices.find(v => v.name === state.selectedMsTTSVoiceName);
  if (!voice) {
    ui.updateStatus('Selected Microsoft TTS voice is invalid.');
    return;
  }

  state.setIsMsTTSReprocessing(true);
  ui.updateStatus(`Reprocessing "${state.currentDocumentFile.name}" with Microsoft TTS (${voice.label})…`);
  ui.updateAudioControlsUI();

  try {
    let text = '';
    if (state.isDocEditing) {
      text = dom.docEditorTextarea.value;
    } else {
      // Handle both txt (pre element) and docx (HTML content) files
      if (state.currentDocumentFile.type === 'txt') {
        text = dom.docHtmlViewer.querySelector('pre')?.textContent || '';
      } else if (state.currentDocumentFile.type === 'docx') {
        // For DOCX files, use the stored plain text content if available
        // This preserves SSML markup better than extracting from HTML
        if (state.currentDocumentFile.plainTextContent) {
          text = state.currentDocumentFile.plainTextContent;
        } else {
          // Fallback to extracting from HTML content
          text = dom.docHtmlViewer.textContent || dom.docHtmlViewer.innerText || '';
        }
      }
    }

    const sliderValue = dom.msTtsSpeedSlider ? parseFloat(dom.msTtsSpeedSlider.value) : 1.0;
    const speakingRate = typeof sliderValue === 'number' ? `${Math.round(sliderValue * 100)}%` : MS_TTS_SPEECH_SPEED;
    const { audioBlob, mimeType } = await synthesizeWithMicrosoftTTS(text, voice.name, voice.languageCode, speakingRate);

    const ext  = state.currentAudioFile.name.slice(state.currentAudioFile.name.lastIndexOf('.')).toLowerCase();
    const base = state.currentAudioFile.name.replace(/^\((G-TTS|MS-TTS)\)\s*/, '').replace(ext, '');
    const fileName = `(MS-TTS) ${base}${ext}`;
    const url = URL.createObjectURL(audioBlob);

    const entry = {
      id: `${fileName}_${Date.now()}`,
      file: new File([audioBlob], fileName, { type: mimeType }),
      name: fileName,
      duration: 0,
      objectURL: url,
      isSynthesized: true,
      ttsProvider: 'microsoft',
    };

    const tmp = new Audio(url);
    tmp.onloadedmetadata = () => {
      entry.duration = tmp.duration;

      // Store original audio info BEFORE calling updateMusicFileListWithNewTTS
      const originalAudioId = state.currentAudioFile.id;
      const originalAudioFile = state.currentAudioFile;
      console.log('Microsoft TTS: Storing original audio ID:', originalAudioId, 'for file:', originalAudioFile.name);

      updateMusicFileListWithNewTTS(entry, 'Microsoft TTS');

      // Track this TTS audio for potential saving/overwriting
      state.setLatestTtsAudioForSave({
        audioEntry: entry,
        originalAudioId: originalAudioId,
        originalAudioFile: originalAudioFile, // Store the full original file info
        provider: 'microsoft'
      });
      console.log('Microsoft TTS: Set latestTtsAudioForSave:', state.latestTtsAudioForSave);
      ui.updateAudioControlsUI(); // Update save button state
    };
    tmp.onerror = () => {
      // Store original audio info BEFORE calling updateMusicFileListWithNewTTS
      const originalAudioId = state.currentAudioFile.id;
      const originalAudioFile = state.currentAudioFile;

      updateMusicFileListWithNewTTS(entry, 'Microsoft TTS');
      // Still track for saving even if metadata loading failed
      state.setLatestTtsAudioForSave({
        audioEntry: entry,
        originalAudioId: originalAudioId,
        originalAudioFile: originalAudioFile,
        provider: 'microsoft'
      });
      ui.updateAudioControlsUI();
    };
  } catch (err) {
    console.error('Microsoft TTS error:', err);
    ui.updateStatus(`Error during Microsoft TTS reprocessing: ${err.message}`);
  } finally {
    state.setIsMsTTSReprocessing(false);
    ui.updateAudioControlsUI();
  }
}

/*===================================================================
  Verification helper (Google + Microsoft)
===================================================================*/
export async function synthesizeTextToAudio(
  text,
  voiceName,
  provider,
  originalBase,
  originalExt,
) {
  let audioData, label, lang;

  if (provider === 'google') {
    const v = state.fetchedGTTSVoices.find(vo => vo.name === voiceName);
    if (!v) throw new Error(`Google voice ${voiceName} not found.`);
    lang = v.languageCode;

    const enc = EXTENSION_TO_ENCODING_GOOGLE[originalExt] || 'MP3';

    const speakingRate = dom.gTtsSpeedSlider ? parseFloat(dom.gTtsSpeedSlider.value) : G_TTS_SPEECH_SPEED;
    const isChirp = isChirp3HDVoiceName(voiceName);
    if (isChirp) {
      const plain = /^<speak\b/i.test(text.trim()) ? stripSSMLToPlainText(text) : text;
      audioData = await synthesizeWithGoogleTTSText(plain, voiceName, lang, enc, G_TTS_SAMPLE_RATE_HERTZ, speakingRate, false);
    } else {
      // Build SSML for Google Cloud TTS and fall back to text if SSML fails
      const ssml = /^<speak\b/i.test(text.trim())
        ? text
        : `<speak><prosody rate="${speakingRate}">${ssmlEscapeXmlEntities(text)}</prosody></speak>`;
      try {
        // Only pass speakingRate to API if SSML doesn't already contain prosody rate
        const hasProsodyRate = /<prosody[^>]*rate=/.test(ssml);
        audioData = await synthesizeWithGoogleTTS(ssml, voiceName, lang, enc, G_TTS_SAMPLE_RATE_HERTZ, hasProsodyRate ? undefined : speakingRate);
      } catch (err) {
        const msg = (err && err.message ? err.message : '').toLowerCase();
        if (msg.includes('ssml')) {
          const plain2 = /^<speak\b/i.test(text.trim()) ? stripSSMLToPlainText(text) : text;
          audioData = await synthesizeWithGoogleTTSText(plain2, voiceName, lang, enc, G_TTS_SAMPLE_RATE_HERTZ, speakingRate, false);
        } else {
          throw err;
        }
      }
    }
    label = 'Google Cloud TTS';
  } else if (provider === 'microsoft') {
    const v = state.fetchedMsTTSVoices.find(vo => vo.name === voiceName);
    if (!v) throw new Error(`Microsoft voice ${voiceName} not found.`);
    lang = v.languageCode;
    const sliderValue = dom.msTtsSpeedSlider ? parseFloat(dom.msTtsSpeedSlider.value) : 1.0;
    const speakingRate = typeof sliderValue === 'number' ? `${Math.round(sliderValue * 100)}%` : MS_TTS_SPEECH_SPEED;
    audioData = await synthesizeWithMicrosoftTTS(text, voiceName, lang, speakingRate);
    label = 'Microsoft TTS';
  } else {
    throw new Error(`Unsupported provider: ${provider}`);
  }

  const { audioBlob, mimeType } = audioData;
  const url  = URL.createObjectURL(audioBlob);
  const file = `(${provider.toUpperCase()}-Verified) ${originalBase}${originalExt}`;

  return new Promise(resolve => {
    const entry = {
      id: `${file}_${Date.now()}`,
      file: new File([audioBlob], file, { type: mimeType }),
      name: file,
      duration: 0,
      objectURL: url,
      isSynthesized: true,
      ttsProvider: provider,
      voiceName,
    };

    const tmp = new Audio(url);
    tmp.onloadedmetadata = () => {
      entry.duration = tmp.duration;
      console.log(`${label} synthesized verification clip: ${entry.name} (${entry.duration}s)`);
      resolve(entry);
    };
    tmp.onerror = () => resolve(entry);
  });
}

/*===================================================================
  TTS Save/Overwrite Functionality
===================================================================*/

/** Try saving a blob via File System Access API; fall back to browser download */
async function saveAudioBlobAuto(audioBlob, suggestedName, mimeType) {
  try {
    if ('showSaveFilePicker' in window) {
      const pickerOpts = {
        suggestedName,
        types: [{
          description: 'Audio file',
          accept: { [mimeType || 'audio/mpeg']: ['.mp3', '.wav', '.ogg', '.flac'] },
        }],
      };
      const handle = await window.showSaveFilePicker(pickerOpts);
      const writable = await handle.createWritable();
      await writable.write(audioBlob);
      await writable.close();
      return { method: 'picker', fileName: handle.name };
    }
  } catch (e) {
    console.warn('File System Access API save failed, falling back to download:', e);
  }
  // Fallback – trigger a standard browser download
  const url = URL.createObjectURL(audioBlob);
  const a = document.createElement('a');
  a.href = url;
  a.download = suggestedName;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  return { method: 'download', fileName: suggestedName };
}

/** Replace an existing playlist entry's audio with new blob, preserving ID and name */
async function replacePlaylistEntryWithBlob(originalEntry, audioBlob, mimeType) {
  const newUrl = URL.createObjectURL(audioBlob);
  if (originalEntry.objectURL && originalEntry.objectURL !== newUrl) {
    try { URL.revokeObjectURL(originalEntry.objectURL); } catch {}
  }
  const updated = {
    ...originalEntry,
    file: new File([audioBlob], originalEntry.name, { type: mimeType }),
    objectURL: newUrl,
    isSynthesized: true,
  };
  // Update duration
  await new Promise(resolve => {
    const tmp = new Audio(newUrl);
    tmp.onloadedmetadata = () => { updated.duration = tmp.duration; resolve(); };
    tmp.onerror = () => resolve();
  });
  const idx = state.musicFiles.findIndex(f => f.id === originalEntry.id);
  if (idx !== -1) state.musicFiles[idx] = updated;
  return updated;
}

/** Clear TTS save state - called when track changes or save is completed */
export function clearTtsSaveState() {
  state.setLatestTtsAudioForSave(null);
  ui.updateAudioControlsUI();
}

/** Save TTS-generated audio to overwrite the original file */
export async function handleSaveTtsOverwrite() {
  console.log('handleSaveTtsOverwrite called');
  console.log('latestTtsAudioForSave:', state.latestTtsAudioForSave);

  if (!state.latestTtsAudioForSave) {
    ui.updateStatus('No TTS audio available to save.');
    return;
  }

  const { audioEntry, originalAudioId, originalAudioFile, provider } = state.latestTtsAudioForSave;

  console.log('Looking for original audio with ID:', originalAudioId);
  console.log('Current musicFiles:', state.musicFiles.map(f => ({ id: f.id, name: f.name })));

  // Find the original audio file - try by ID first, then by name as fallback
  let originalIndex = state.musicFiles.findIndex(f => f.id === originalAudioId);
  let originalFile = null;

  if (originalIndex !== -1) {
    originalFile = state.musicFiles[originalIndex];
    console.log('Found original file by ID:', originalFile);
  } else {
    // Fallback: try to find by name (in case the ID changed)
    console.log('Original file not found by ID, trying by name:', originalAudioFile?.name);
    originalIndex = state.musicFiles.findIndex(f =>
      f.name === originalAudioFile?.name && !f.name.startsWith('(G-TTS)') && !f.name.startsWith('(MS-TTS)')
    );

    if (originalIndex !== -1) {
      originalFile = state.musicFiles[originalIndex];
      console.log('Found original file by name:', originalFile);
    } else {
      ui.updateStatus('Error: Original audio file not found.');
      console.error('Original audio file not found. ID:', originalAudioId, 'Name:', originalAudioFile?.name);
      return;
    }
  }
  console.log('Found original file:', originalFile);

  // Show confirmation dialog
  const confirmed = confirm(
    `Are you sure you want to overwrite "${originalFile.name}" with the ${provider.toUpperCase()} TTS version?\n\n` +
    `This action cannot be undone. The original audio file will be permanently replaced.`
  );

  if (!confirmed) {
    ui.updateStatus('Save operation cancelled.');
    return;
  }

  try {
    ui.updateStatus(`Saving ${provider.toUpperCase()} TTS audio to overwrite original file...`);
    console.log('Starting file replacement process...');

    // Create the updated audio entry with the original name and ID
    const updatedAudioEntry = {
      ...audioEntry,
      id: originalFile.id, // Keep the original ID
      name: originalFile.name, // Keep the original name
      isSynthesized: true,
      ttsProvider: provider
    };

    console.log('Created updated audio entry:', updatedAudioEntry);

    // Revoke the old object URL if it exists
    if (originalFile.objectURL && originalFile.objectURL !== audioEntry.objectURL) {
      console.log('Revoking old object URL:', originalFile.objectURL);
      URL.revokeObjectURL(originalFile.objectURL);
    }

    // Replace the original file in the playlist
    console.log('Replacing file at index:', originalIndex);
    state.musicFiles[originalIndex] = updatedAudioEntry;

    // Remove the TTS-prefixed version from the playlist if it exists
    const ttsIndex = state.musicFiles.findIndex(f => f.id === audioEntry.id);
    console.log('TTS file index to remove:', ttsIndex);
    if (ttsIndex !== -1 && ttsIndex !== originalIndex) {
      const ttsFile = state.musicFiles[ttsIndex];
      if (ttsFile.objectURL) {
        console.log('Revoking TTS object URL:', ttsFile.objectURL);
        URL.revokeObjectURL(ttsFile.objectURL);
      }
      state.musicFiles.splice(ttsIndex, 1);
      console.log('Removed TTS file from playlist');
    }

    // Update the current audio file if it was the one being replaced
    if (state.currentTrackId === originalFile.id) {
      console.log('Updating current audio file and reloading player');
      state.setCurrentAudioFile(updatedAudioEntry);
      // Reload the audio player with the new file
      const wasPlaying = state.isPlaying;
      await selectMusicTrack(updatedAudioEntry.id, wasPlaying);
    }

    // Clear the save state
    state.setLatestTtsAudioForSave(null);
    console.log('Cleared TTS save state');

    // Update UI
    ui.renderMusicPlaylist();
    ui.updateAudioControlsUI();
    console.log('Updated UI');

    ui.updateStatus(`Successfully saved ${provider.toUpperCase()} TTS audio and overwritten "${originalFile.name}".`);
    console.log('File replacement completed successfully');

    // Refresh highlighting to ensure synchronization
    setTimeout(() => refreshHighlighting(), 300);

  } catch (error) {
    console.error('Error saving TTS audio:', error);
    ui.updateStatus(`Error saving TTS audio: ${error.message}`);
  }
}
