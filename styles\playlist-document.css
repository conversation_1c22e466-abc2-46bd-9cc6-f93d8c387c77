/* Add this new ruleset to playlist-document.css */
.playlist-area {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    min-height: 0; /* This is the key property */
}

/* Also add this to ensure the heading behaves correctly */
.playlist-area h2 {
    flex-shrink: 0;
}

.playlist-panel {
    flex: 1 1 350px; 
    overflow: hidden;
}

.document-panel {
    flex: 1.5 1 450px; 
    display: flex; 
    flex-direction: column;
}

/* Split layout between Playlist and Documents */
#playlist-doc-split {
    display: grid;
    grid-template-columns: minmax(240px, 1fr) 8px minmax(380px, 2fr);
    gap: 15px;
    width: 100%;
    align-items: stretch;
    min-width: 0; /* allow children to shrink */
}

#playlist-doc-split > .panel {
    min-width: 0; /* prevent grid items from forcing overflow */
}

.playlist-doc-resizer {
    width: 8px;
    background: linear-gradient(180deg, rgba(176,158,128,0.1), rgba(176,158,128,0.25));
    border: 1px solid rgba(176,158,128,0.35);
    border-radius: 6px;
    cursor: col-resize;
}

.playlist-doc-resizer:focus-visible {
    outline: 2px solid var(--accent-primary);
}

@media (max-width: 1100px) {
    #playlist-doc-split {
        grid-template-columns: 1fr;
    }
    .playlist-doc-resizer { display: none; }
}

/* Ensure panels can shrink without clipping contents */
.playlist-panel { min-width: 0; }
.document-panel { min-width: 0; }

.document-area {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    min-height: 0; 
}

.doc-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.doc-header h3 { /* This is specific to doc-header h3, general h3 is in base.css */
    margin: 0;
    font-size: 1.1em;
    color: var(--text-panel-heading);
}

.doc-controls {
    display: flex;
    gap: 10px;
}

.document-viewer-editor {
    border: 1px solid var(--border-primary);
    padding: 10px;
    background-color: var(--bg-secondary); 
    flex-grow: 1; 
    overflow-y: auto;
    min-height: 150px; 
    font-size: 0.95em;
    line-height: 1.6;
    display: grid;
    grid-template-columns: 1fr 1fr; /* text | pdf */
    grid-template-rows: minmax(0, 1fr);
    align-items: stretch;
    gap: 12px;
    color: var(--text-primary);
}

#doc-editor-textarea { /* Target by ID */
    width: 100%;
    height: 100%;
    min-height: 300px;
    border: none;
    resize: none;
    box-sizing: border-box;
    font-family: monospace;
    font-size: 1em;
    outline: none;
    background-color: transparent; 
    color: var(--text-primary);
}

.document-viewer-editor pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0; 
}

.document-viewer-editor .doc-html-content { /* if used */
    word-wrap: break-word;
}

.list-box {
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-y: auto;
    border: 1px solid var(--border-primary);
    background-color: var(--bg-list-box);
    flex-grow: 1; 
    border-radius: 4px;
    min-height: 0; 
}
.list-box .empty-state { /* For no items message */
    padding: 10px;
    text-align: center;
    color: var(--text-placeholder);
    list-style: none;
}

.list-box li {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid var(--border-secondary);
    font-size: 0.9em;
    display: flex; 
    align-items: center; 
    flex-wrap: wrap; 
    gap: 5px 10px; 
    color: var(--text-primary);
}

.list-box li .material-icons { /* Style for icons in playlist items */
    font-size: 1.1em; 
    margin-right: 8px;
    color: var(--icon-primary); 
    vertical-align: middle; 
}

.list-box li .material-icons.ms-tts-icon {
    color: var(--icon-ms-tts); 
}

.list-box li:last-child {
    border-bottom: none;
}

.list-box li:hover, .list-box li:focus {
    background-color: var(--bg-list-item-hover);
    outline: none;
}

/* Stronger keyboard focus outline for accessibility */
.list-box li:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: -2px;
}

.list-box li.selected {
    background-color: var(--bg-list-item-selected);
    color: var(--text-list-item-selected);
    font-weight: 500;
}

.list-box li.selected .material-icons { 
    color: var(--icon-selected-item);
}

.highlight {
    background-color: var(--accent-highlight);
    color: var(--text-highlighted);
    padding: 0.1em 0;
    border-radius: 2px;
}

.search-highlight { /* For SSML search */
    background-color: var(--accent-search-highlight);
    color: var(--text-search-highlighted);
}