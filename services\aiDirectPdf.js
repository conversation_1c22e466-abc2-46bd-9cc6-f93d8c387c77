/*
  aiDirectPdf.js
  - PDF viewer with text layer
  - Real-time TTS narration by page/sentences
  - Live editable transcript with timestamps
  - Segment caching and MP3 export (client-side merge)

  Notes:
  - Uses existing Google/Azure TTS via ttsService.
  - Uses pdf.js for page rendering and text extraction.
  - Streams audio by sentences; keeps <500ms latency by prefetching next.
*/

import {
  DEFAULT_G_TTS_VOICE_NAME,
  DEFAULT_MS_TTS_VOICE_NAME,
  G_TTS_API_KEY,
  G_TTS_SAMPLE_RATE_HERTZ,
  MS_TTS_API_KEY,
  MS_TTS_SERVICE_REGION,
  MS_TTS_SAMPLE_RATE_HERTZ,
} from '../constants.js';
import { formatTime, base64ToBlob, ssmlEscapeXmlEntities } from '../utils.js';

// DOM refs
const el = {
  fileInput: document.getElementById('aidpdf-file-input'),
  openSample: document.getElementById('aidpdf-open-sample'),
  provider: document.getElementById('aidpdf-provider'),
  voice: document.getElementById('aidpdf-voice'),
  speed: document.getElementById('aidpdf-speed'),
  speedVal: document.getElementById('aidpdf-speed-value'),
  refreshVoices: document.getElementById('aidpdf-refresh-voices'),
  play: document.getElementById('aidpdf-play'),
  pause: document.getElementById('aidpdf-pause'),
  stop: document.getElementById('aidpdf-stop'),
  back10: document.getElementById('aidpdf-skip-back'),
  fwd10: document.getElementById('aidpdf-skip-fwd'),
  download: document.getElementById('aidpdf-download'),
  progress: document.getElementById('aidpdf-progress'),
  tCur: document.getElementById('aidpdf-current-time'),
  tTotal: document.getElementById('aidpdf-total-time'),
  canvas: document.getElementById('aidpdf-canvas'),
  textLayer: document.getElementById('aidpdf-text-layer'),
  prevPage: document.getElementById('aidpdf-prev-page'),
  nextPage: document.getElementById('aidpdf-next-page'),
  pageNum: document.getElementById('aidpdf-page-number'),
  pageCount: document.getElementById('aidpdf-page-count'),
  transcript: document.getElementById('aidpdf-transcript'),
  regenerate: document.getElementById('aidpdf-regenerate'),
  style: document.getElementById('aidpdf-style'),
  ssml: document.getElementById('aidpdf-ssml'),
  instructions: document.getElementById('aidpdf-instructions'),
  useGemini: document.getElementById('aidpdf-use-gemini'),
  geminiModel: document.getElementById('aidpdf-gemini-model'),
};

// State
const state = {
  pdfDoc: null,
  page: 1,
  totalPages: 0,
  pageTextItems: new Map(), // page -> [{str, transform}]
  reading: false,
  audioCtx: null,
  masterGain: null,
  currentSource: null,
  queue: [], // queued {audioBuffer, startTime, duration, segId}
  nowOffset: 0, // scheduled time offset in audioCtx
  segmentCache: new Map(), // segId -> { start, end, blob, audioBuffer, provider, voiceName, text }
  segmentsIndex: [], // ordered segIds for export
  curSegIdx: 0,
  provider: 'google',
  voiceName: '',
  speed: 1.0,
  currentMp3Blob: null,
  sentenceMap: new Map(), // segId -> { page, text, start, end }
  style: 'neutral',
  ssmlSnippet: '',
  instructions: '',
  useGeminiVision: false,
  geminiModel: 'gemini-2.5-flash',
};

// Detect Chirp3 HD-capable Google voices by name
function isChirp3HDVoiceName(name) {
  if (!name) return false;
  const n = String(name).toLowerCase();
  return /(chirp\s*-?\s*3)/i.test(name) || (n.includes('chirp') && n.includes('hd'));
}


function persistPrefs() {
  localStorage.setItem('aidpdf_prefs', JSON.stringify({ provider: state.provider, voiceName: state.voiceName, speed: state.speed, style: state.style, ssmlSnippet: state.ssmlSnippet, instructions: state.instructions, useGeminiVision: state.useGeminiVision, geminiModel: state.geminiModel }));
}
function loadPrefs() {
  try {
    const raw = localStorage.getItem('aidpdf_prefs');
    if (!raw) return;
    const prefs = JSON.parse(raw);
    if (prefs.provider) state.provider = prefs.provider;
    if (prefs.voiceName) state.voiceName = prefs.voiceName;
    if (prefs.speed) state.speed = Number(prefs.speed) || 1.0;
    if (prefs.style) state.style = prefs.style;
    if (typeof prefs.ssmlSnippet === 'string') state.ssmlSnippet = prefs.ssmlSnippet;
    if (typeof prefs.instructions === 'string') state.instructions = prefs.instructions;
    if (typeof prefs.useGeminiVision === 'boolean') state.useGeminiVision = prefs.useGeminiVision;
    if (typeof prefs.geminiModel === 'string') state.geminiModel = prefs.geminiModel;
  } catch {}
}

function initAudio() {
  if (!state.audioCtx) {
    state.audioCtx = new (window.AudioContext || window.webkitAudioContext)();
    state.masterGain = state.audioCtx.createGain();
    state.masterGain.connect(state.audioCtx.destination);
  }
}

async function renderPage(n) {
  if (!state.pdfDoc) return;
  const page = await state.pdfDoc.getPage(n);
  const viewport = page.getViewport({ scale: 1.5 });
  const ctx = el.canvas.getContext('2d');
  el.canvas.width = viewport.width;
  el.canvas.height = viewport.height;
  await page.render({ canvasContext: ctx, viewport }).promise;

  // Text layer
  const textContent = await page.getTextContent();
  el.textLayer.innerHTML = '';
  el.textLayer.style.width = `${viewport.width}px`;
  el.textLayer.style.height = `${viewport.height}px`;
  state.pageTextItems.set(n, textContent.items);
  for (const item of textContent.items) {
    const span = document.createElement('span');
    span.textContent = item.str;
    const tx = window.pdfjsLib.Util.transform(viewport.transform, item.transform);
    const x = tx[4];
    const fontHeight = Math.hypot(tx[2], tx[3]);
    const y = tx[5] - fontHeight;
    const angle = Math.atan2(tx[1], tx[0]);
    span.style.left = `${x}px`;
    span.style.top = `${y}px`;
    span.style.fontSize = `${fontHeight}px`;
    span.style.transform = `rotate(${angle}rad)`;
    el.textLayer.appendChild(span);
  }
}

async function openPdf(fileOrUrl) {
  const data = typeof fileOrUrl === 'string'
    ? await fetch(fileOrUrl).then(r => r.arrayBuffer())
    : await fileOrUrl.arrayBuffer();
  if (typeof window.pdfjsLib !== 'undefined') {
    window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
  }
  const loading = window.pdfjsLib.getDocument({ data });
  state.pdfDoc = await loading.promise;
  state.page = 1;
  state.totalPages = state.pdfDoc.numPages;
  el.pageNum.value = '1';
  el.pageCount.textContent = `/ ${state.totalPages}`;
  await renderPage(1);
}

function splitIntoSentences(text) {
  // Lightweight sentence splitter
  const base = (text || '')
    .replace(/\s+/g, ' ')
    .trim()
    .match(/[^.!?\n]+[.!?\n]?/g) || [];
  // Further split long sentences into phrases (commas/colon/semicolon) to reduce latency
  const chunks = [];
  for (const s of base) {
    if (s.length <= 140) { chunks.push(s.trim()); continue; }
    const parts = s.split(/([,;:])\s+/);
    let buf = '';
    for (let i = 0; i < parts.length; i++) {
      buf += parts[i];
      if (buf.length >= 100) { chunks.push(buf.trim()); buf = ''; }
    }
    if (buf.trim()) chunks.push(buf.trim());
  }
  return chunks.filter(Boolean);
}

async function getPagePlainText(n) {
  if (state.useGeminiVision) {
    try {
      const page = await state.pdfDoc.getPage(n);
      // Render page to an offscreen canvas and export as PNG base64
      const viewport = page.getViewport({ scale: 1.5 });
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = viewport.width;
      canvas.height = viewport.height;
      await page.render({ canvasContext: ctx, viewport }).promise;
      const dataUrl = canvas.toDataURL('image/png');
      const base64 = dataUrl.replace(/^data:image\/png;base64,/, '');

      const instructions = (state.instructions || '').trim();
      const rules = instructions.length > 0 ? instructions : 'Read all visible text exactly as it appears.';

      // Build Gemini payload per latest models
      const modelId = state.geminiModel || 'gemini-2.5-flash';
      const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_GEMINI_API_KEY) || null;
      const modelUrlMap = {
        'gemini-2.5-flash': 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent',
        'gemini-2.5-pro': 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent',
      };
      const url = modelUrlMap[modelId] || modelUrlMap['gemini-2.5-flash'];

      if (!apiKey) {
        console.warn('Gemini API key not set; falling back to embedded text layer.');
      } else {
        const prompt = `You are producing final audiobook narration text from a PDF page image. Apply these rules strictly and output only the narration text:\n\n${rules}\n\nDo not describe the image. If no readable text, return an empty string.`;
        const payload = {
          contents: [{
            parts: [
              { text: prompt },
              { inline_data: { mime_type: 'image/png', data: base64 } }
            ]
          }],
          generationConfig: { temperature: 0.2, maxOutputTokens: 60000 }
        };
        const res = await fetch(`${url}?key=${apiKey}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        });
        if (res.ok) {
          const data = await res.json();
          const txt = data?.candidates?.[0]?.content?.parts?.map(p => p.text || '').join('')?.trim();
          if (txt) return txt;
        } else {
          console.warn('Gemini Vision request failed:', res.status, res.statusText);
        }
      }
    } catch (err) {
      console.warn('Gemini Vision pipeline error; falling back to text layer:', err);
    }
  }
  const items = state.pageTextItems.get(n) || [];
  return items.map(it => it.str).join(' ');
}

function clearTranscript() { el.transcript.innerHTML = ''; state.sentenceMap.clear(); state.segmentsIndex = []; state.curSegIdx = 0; }

function appendTranscriptSegment(segId, text, start, end) {
  state.sentenceMap.set(segId, { page: state.page, text, start, end });
  state.segmentsIndex.push(segId);
  const div = document.createElement('div');
  div.className = 'seg';
  div.dataset.segId = segId;
  const meta = document.createElement('div');
  meta.className = 'meta';
  meta.textContent = `[${formatTime(start)} – ${formatTime(end)}]`;
  const editable = document.createElement('div');
  editable.className = 'text';
  editable.contentEditable = 'true';
  editable.textContent = text;
  div.appendChild(meta);
  div.appendChild(editable);
  el.transcript.appendChild(div);
}

function highlightCurrentSegment(segId) {
  el.transcript.querySelectorAll('.seg').forEach(s => s.classList.toggle('active', s.dataset.segId === segId));
}

function highlightCurrentSentenceInPdf(text) {
  const spans = el.textLayer.querySelectorAll('span');
  spans.forEach(s => s.classList.remove('reading-highlight'));
  if (!text) return;
  const norm = (s) => s.toLowerCase().replace(/\s+/g, ' ').trim();
  const target = norm(text);
  // naive search: mark spans that include any token from target
  const tokens = target.split(' ').filter(Boolean);
  let firstMarked = null;
  for (const span of spans) {
    const n = norm(span.textContent || '');
    if (tokens.length && tokens.some(t => n.includes(t))) {
      span.classList.add('reading-highlight');
      if (!firstMarked) firstMarked = span;
    }
  }
  if (firstMarked) firstMarked.scrollIntoView({ block: 'center', behavior: 'smooth' });
}

function getStyleParams() {
  switch (state.style) {
    case 'reverent':
      return { pitch: '-2st', emphasis: 'reduced' };
    case 'dramatic':
      return { pitch: '+2st', emphasis: 'strong' };
    case 'academic':
      return { pitch: null, emphasis: 'none' };
    case 'pro-audiobook':
      return { pitch: '+1st', emphasis: 'moderate' };
    case 'neutral':
    default:
      return { pitch: null, emphasis: 'none' };
  }
}

function buildInnerContentForSSML(text) {
  const escaped = ssmlEscapeXmlEntities(text);
  const { emphasis } = getStyleParams();
  let content = escaped;
  // Apply simple natural-language instruction transformations
  const instr = (state.instructions || '').toLowerCase();
  if (instr.includes('pause at comma') || instr.includes('pause at commas')) {
    // Insert a short break after commas
    content = content.replace(/,\s*/g, ',<break strength="weak"/> ');
  }
  if (instr.includes('emphasize quotes') || instr.includes('emphasise quotes') || instr.includes('emphasize quoted')) {
    // Wrap quoted segments with emphasis
    content = content.replace(/"([^"]+)"/g, '<emphasis level="moderate">"$1"</emphasis>');
    content = content.replace(/'([^']+)'/g, '<emphasis level="moderate">&#39;$1&#39;</emphasis>');
  }
  if (emphasis && emphasis !== 'none') {
    content = `<emphasis level="${emphasis}">${content}</emphasis>`;
  }
  const snippet = (state.ssmlSnippet || '').trim();
  if (snippet) {
    if (snippet.includes('{{text}}')) {
      try {
        content = snippet.split('{{text}}').join(content);
      } catch {
        content = content + snippet;
      }
    } else {
      content = content + snippet;
    }
  }
  return content;
}

function buildGoogleSSML(text) {
  const { pitch } = getStyleParams();
  const parts = [`rate="${(state.speed * 100).toFixed(0)}%"`];
  if (pitch) parts.push(`pitch="${pitch}"`);
  const inner = buildInnerContentForSSML(text);
  return `<speak><prosody ${parts.join(' ')}>${inner}</prosody></speak>`;
}

function buildMicrosoftSSML(text, voiceName) {
  const { pitch } = getStyleParams();
  const pitchAttr = pitch ? ` pitch='${pitch}'` : '';
  const inner = buildInnerContentForSSML(text);
  return `<speak version='1.0'><voice name='${voiceName}'><prosody rate='${(state.speed * 100).toFixed(0)}%'${pitchAttr}>${inner}</prosody></voice></speak>`;
}

async function synthesizeSentence(text, baseName) {
  const provider = state.provider;
  const voiceName = state.voiceName;
  if (provider === 'google') {
    if (!G_TTS_API_KEY) throw new Error('Google TTS key missing');
    const langCode = voiceName.split('-').slice(0,2).join('-');

    let res;
    if (isChirp3HDVoiceName(voiceName)) {
      // Chirp3 HD: send plain text and control pace via speakingRate (no SSML)
      res = await fetch(`https://texttospeech.googleapis.com/v1/text:synthesize?key=${G_TTS_API_KEY}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          input: { text },
          voice: { languageCode: langCode, name: voiceName },
          audioConfig: { audioEncoding: 'MP3', sampleRateHertz: G_TTS_SAMPLE_RATE_HERTZ, speakingRate: state.speed },
        }),
      });
      if (!res.ok) throw new Error('Google TTS synth failed');
    } else {
      // Standard path: try SSML first, then fallback to text on SSML errors
      const ssml = buildGoogleSSML(text);
      res = await fetch(`https://texttospeech.googleapis.com/v1/text:synthesize?key=${G_TTS_API_KEY}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          input: { ssml },
          voice: { languageCode: langCode, name: voiceName },
          audioConfig: { audioEncoding: 'MP3', sampleRateHertz: G_TTS_SAMPLE_RATE_HERTZ },
        }),
      });
      if (!res.ok) {
        let msg = '';
        try { const data = await res.json(); msg = data?.error?.message || ''; } catch {
          try { msg = await res.text(); } catch {}
        }
        if ((msg || '').toLowerCase().includes('ssml')) {
          // Fallback to plain text for voices that do not support SSML
          res = await fetch(`https://texttospeech.googleapis.com/v1/text:synthesize?key=${G_TTS_API_KEY}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              input: { text },
              voice: { languageCode: langCode, name: voiceName },
              audioConfig: { audioEncoding: 'MP3', sampleRateHertz: G_TTS_SAMPLE_RATE_HERTZ },
            }),
          });
          if (!res.ok) throw new Error(msg || 'Google TTS synth failed');
        } else {
          throw new Error(msg || 'Google TTS synth failed');
        }
      }
    }

    const { audioContent } = await res.json();
    const blob = base64ToBlob(audioContent, 'audio/mpeg');
    const arrayBuffer = await blob.arrayBuffer();
    const audioBuffer = await state.audioCtx.decodeAudioData(arrayBuffer);
    return { audioBuffer, blob, duration: audioBuffer.duration };
  } else {
    if (!MS_TTS_API_KEY || !MS_TTS_SERVICE_REGION) throw new Error('MS TTS config missing');
    const wrapped = buildMicrosoftSSML(text, voiceName);
    const res = await fetch(`https://${MS_TTS_SERVICE_REGION}.tts.speech.microsoft.com/cognitiveservices/v1`, {
      method: 'POST',
      headers: {
        'Ocp-Apim-Subscription-Key': MS_TTS_API_KEY,
        'Content-Type': 'application/ssml+xml',
        'X-Microsoft-OutputFormat': MS_TTS_SAMPLE_RATE_HERTZ,
      },
      body: wrapped,
    });
    if (!res.ok) throw new Error('MS TTS synth failed');
    const buf = await res.arrayBuffer();
    const blob = new Blob([buf], { type: 'audio/mpeg' });
    const audioBuffer = await state.audioCtx.decodeAudioData(buf.slice(0));
    return { audioBuffer, blob, duration: audioBuffer.duration };
  }
}

function getTotalDuration() {
  const lastId = state.segmentsIndex[state.segmentsIndex.length - 1];
  if (!lastId) return 0;
  const m = state.segmentCache.get(lastId);
  return m ? m.end : 0;
}

function getCurrentSegIdAt(timeSec) {
  for (const id of state.segmentsIndex) {
    const m = state.segmentCache.get(id);
    if (m && timeSec >= m.start && timeSec < m.end) return id;
  }
  return null;
}

function updateActiveHighlightsLoop() {
  if (!state.audioCtx) return;
  const t = state.audioCtx.currentTime;
  const segId = getCurrentSegIdAt(t);
  if (segId) {
    highlightCurrentSegment(segId);
    const meta = state.segmentCache.get(segId);
    if (meta) {
      highlightCurrentSentenceInPdf(meta.text);
      const segEl = el.transcript.querySelector(`[data-seg-id="${segId}"]`) || el.transcript.querySelector(`.seg[data-seg-id="${segId}"]`);
      const activeEl = segEl || el.transcript.querySelector('.seg.active');
      if (activeEl) activeEl.scrollIntoView({ block: 'nearest' });
    }
  }
  updateProgressUI();
}

async function scheduleSegmentsForPage(pageNum) {
  initAudio();
  const baseName = `Page-${pageNum}-seg`;
  const text = await getPagePlainText(pageNum);
  const sentences = splitIntoSentences(text).filter(s => s.trim().length > 0);
  clearTranscript();
  let cursor = state.audioCtx.currentTime + 0.05; // small lead-in
  state.nowOffset = Math.max(state.nowOffset, cursor);
  for (let i = 0; i < sentences.length; i++) {
    const segId = `${baseName}-${i}`;
    // synth sequentially but keep queue short to control latency; prefetch next while current plays
    /* eslint-disable no-await-in-loop */
    const { audioBuffer, blob, duration } = await synthesizeSentence(sentences[i], baseName);
    const source = state.audioCtx.createBufferSource();
    source.buffer = audioBuffer;
    source.playbackRate.value = state.speed;
    source.connect(state.masterGain);
    const startAt = state.nowOffset;
    source.start(startAt);
    state.nowOffset += duration / state.speed;
    state.currentSource = source;
    const start = startAt;
    const end = startAt + duration / state.speed;
    state.segmentCache.set(segId, { start, end, blob, audioBuffer, provider: state.provider, voiceName: state.voiceName, text: sentences[i] });
    appendTranscriptSegment(segId, sentences[i], start, end);
  }
  const total = getTotalDuration();
  el.progress.max = total;
  el.tTotal.textContent = formatTime(total);
  requestAnimationFrame(updateActiveHighlightsLoop);
}

function stopPlayback() {
  if (!state.audioCtx) return;
  try { state.audioCtx.close(); } catch {}
  state.audioCtx = null;
  state.masterGain = null;
  state.currentSource = null;
  state.nowOffset = 0;
}

async function regenerateSelected() {
  const sel = document.getSelection();
  if (!sel || sel.rangeCount === 0) return;
  let node = sel.anchorNode;
  while (node && !(node instanceof HTMLElement && node.classList.contains('seg'))) {
    node = node.parentNode;
  }
  if (!node) return;
  const segId = node.dataset.segId;
  const textEl = node.querySelector('.text');
  const newText = textEl.textContent || '';
  const meta = state.segmentCache.get(segId);
  if (!meta) return;

  initAudio();
  const baseName = segId.split('-').slice(0, 2).join('-');
  const { audioBuffer, blob, duration } = await synthesizeSentence(newText, baseName);

  // Replace cached segment and adjust following timestamps
  const oldDur = meta.end - meta.start;
  const newDur = duration / state.speed;
  state.segmentCache.set(segId, { ...meta, blob, audioBuffer, text: newText, start: meta.start, end: meta.start + newDur });
  const idx = state.segmentsIndex.indexOf(segId);
  for (let i = idx + 1; i < state.segmentsIndex.length; i++) {
    const sid = state.segmentsIndex[i];
    const m = state.segmentCache.get(sid);
    const shift = newDur - oldDur;
    state.segmentCache.set(sid, { ...m, start: m.start + shift, end: m.end + shift });
  }
  // Update transcript meta
  node.querySelector('.meta').textContent = `[${formatTime(meta.start)} – ${formatTime(meta.start + newDur)}]`;
  // Update totals
  const total = getTotalDuration();
  el.progress.max = total;
  el.tTotal.textContent = formatTime(total);
}

async function exportMergedMp3() {
  // Client-side merge by concatenating ArrayBuffers of MP3 frames (naive, works for identical encoder params)
  const blobs = state.segmentsIndex.map(id => state.segmentCache.get(id)?.blob).filter(Boolean);
  if (!blobs.length) return;
  const buffers = await Promise.all(blobs.map(b => b.arrayBuffer()));
  const totalLen = buffers.reduce((a, b) => a + b.byteLength, 0);
  const merged = new Uint8Array(totalLen);
  let offset = 0;
  for (const buf of buffers) { merged.set(new Uint8Array(buf), offset); offset += buf.byteLength; }
  const out = new Blob([merged], { type: 'audio/mpeg' });
  const a = document.createElement('a');
  a.href = URL.createObjectURL(out);
  a.download = 'narration.mp3';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

function updateProgressUI() {
  if (!state.audioCtx) return;
  const t = state.audioCtx.currentTime;
  el.tCur.textContent = formatTime(t);
  el.progress.value = t;
}

function restartAt(targetTime) {
  // Stop existing playback and reschedule from targetTime using cached audioBuffers
  if (!state.segmentsIndex.length) return;
  stopPlayback();
  initAudio();
  const startNow = state.audioCtx.currentTime + 0.05;
  let scheduleAt = startNow;
  for (const segId of state.segmentsIndex) {
    const m = state.segmentCache.get(segId);
    if (!m || !m.audioBuffer) continue;
    if (m.end <= targetTime) continue; // before target
    const source = state.audioCtx.createBufferSource();
    source.buffer = m.audioBuffer;
    source.playbackRate.value = state.speed;
    source.connect(state.masterGain);
    const offset = Math.max(0, targetTime - m.start);
    const remain = (m.end - targetTime);
    try { source.start(scheduleAt, offset); } catch {}
    scheduleAt += remain;
  }
  const total = getTotalDuration();
  el.progress.max = total;
  el.tTotal.textContent = formatTime(total);
  requestAnimationFrame(updateActiveHighlightsLoop);
}

async function fetchGoogleVoicesLocal() {
  if (!G_TTS_API_KEY) return [];
  const res = await fetch(`https://texttospeech.googleapis.com/v1/voices?key=${G_TTS_API_KEY}`);
  if (!res.ok) return [];
  const data = await res.json();
  return (data.voices || []).map(v => ({ name: v.name, label: `${v.name} (${v.languageCodes?.[0] || ''})`, languageCode: v.languageCodes?.[0] || '' }));
}
async function fetchMicrosoftVoicesLocal() {
  if (!MS_TTS_API_KEY || !MS_TTS_SERVICE_REGION) return [];
  const res = await fetch(`https://${MS_TTS_SERVICE_REGION}.tts.speech.microsoft.com/cognitiveservices/voices/list`, {
    headers: { 'Ocp-Apim-Subscription-Key': MS_TTS_API_KEY },
  });
  if (!res.ok) return [];
  const raw = await res.json();
  return raw.map(v => ({ name: v.ShortName, label: `${v.ShortName} (${v.Locale}${v.Gender ? ' - ' + v.Gender : ''})`, languageCode: v.Locale }));
}

async function refreshVoicesUI() {
  const prov = state.provider;
  el.voice.innerHTML = '<option>Loading…</option>';
  const list = prov === 'google' ? await fetchGoogleVoicesLocal() : await fetchMicrosoftVoicesLocal();
  el.voice.innerHTML = '';
  for (const v of list) {
    const opt = document.createElement('option');
    opt.value = v.name;
    opt.textContent = v.label;
    el.voice.appendChild(opt);
  }
  // restore pref or default
  const def = prov === 'google' ? DEFAULT_G_TTS_VOICE_NAME : DEFAULT_MS_TTS_VOICE_NAME;
  const target = list.find(v => v.name === state.voiceName) ? state.voiceName : (list.find(v => v.name === def)?.name || list[0]?.name);
  if (target) { el.voice.value = target; state.voiceName = target; }
  persistPrefs();
}

function bindEvents() {
  el.fileInput?.addEventListener('change', async (e) => {
    const f = e.target.files?.[0];
    if (f) await openPdf(f);
  });
  el.openSample?.addEventListener('click', () => openPdf('https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf'));

  el.prevPage?.addEventListener('click', async () => { if (state.page > 1) { state.page -= 1; el.pageNum.value = String(state.page); await renderPage(state.page); }});
  el.nextPage?.addEventListener('click', async () => { if (state.page < state.totalPages) { state.page += 1; el.pageNum.value = String(state.page); await renderPage(state.page); }});
  el.pageNum?.addEventListener('change', async () => {
    let n = Number(el.pageNum.value) || 1; n = Math.max(1, Math.min(state.totalPages || 1, n)); state.page = n; await renderPage(n);
  });

  el.provider?.addEventListener('change', async () => { state.provider = el.provider.value; persistPrefs(); await refreshVoicesUI(); });
  el.voice?.addEventListener('change', () => { state.voiceName = el.voice.value; persistPrefs(); });
  el.speed?.addEventListener('input', () => { state.speed = Number(el.speed.value) || 1.0; el.speedVal.textContent = `${state.speed.toFixed(2)}×`; persistPrefs(); });
  el.refreshVoices?.addEventListener('click', refreshVoicesUI);
  el.style?.addEventListener('change', () => { state.style = el.style.value || 'neutral'; persistPrefs(); });
  el.ssml?.addEventListener('input', () => { state.ssmlSnippet = el.ssml.value || ''; persistPrefs(); });
  el.instructions?.addEventListener('input', () => { state.instructions = el.instructions.value || ''; persistPrefs(); });
  el.useGemini?.addEventListener('change', () => { state.useGeminiVision = !!el.useGemini.checked; persistPrefs(); });
  el.geminiModel?.addEventListener('change', () => { state.geminiModel = el.geminiModel.value || 'gemini-2.5-flash'; persistPrefs(); });

  el.play?.addEventListener('click', async () => {
    if (!state.pdfDoc) return;
    if (!state.audioCtx || state.audioCtx.state === 'closed') initAudio();
    if (state.audioCtx.state === 'suspended') await state.audioCtx.resume();
    state.reading = true;
    await scheduleSegmentsForPage(state.page);
  });
  el.pause?.addEventListener('click', async () => { if (state.audioCtx) await state.audioCtx.suspend(); });
  el.stop?.addEventListener('click', stopPlayback);
  el.back10?.addEventListener('click', () => { if (state.audioCtx) restartAt(Math.max(0, state.audioCtx.currentTime - 10)); });
  el.fwd10?.addEventListener('click', () => { if (state.audioCtx) restartAt(Math.min(getTotalDuration(), state.audioCtx.currentTime + 10)); });
  el.download?.addEventListener('click', exportMergedMp3);

  el.regenerate?.addEventListener('click', regenerateSelected);

  el.transcript?.addEventListener('keyup', () => { el.regenerate.disabled = false; });

  el.progress?.addEventListener('input', () => { const t = Number(el.progress.value) || 0; restartAt(t); });
}

async function bootstrap() {
  loadPrefs();
  // Ensure provider options exist
  if (el.provider) {
    el.provider.innerHTML = '';
    const optG = document.createElement('option');
    optG.value = 'google';
    optG.textContent = 'Google TTS';
    const optM = document.createElement('option');
    optM.value = 'microsoft';
    optM.textContent = 'Microsoft TTS';
    el.provider.appendChild(optG);
    el.provider.appendChild(optM);
  }
  if (el.provider) el.provider.value = state.provider;
  if (el.speed) { el.speed.value = String(state.speed); el.speedVal.textContent = `${state.speed.toFixed(2)}×`; }
  if (el.style) { el.style.value = state.style; }
  if (el.ssml) { el.ssml.value = state.ssmlSnippet; }
  if (el.instructions) { el.instructions.value = state.instructions; }
  if (el.useGemini) { el.useGemini.checked = !!state.useGeminiVision; }
  if (el.geminiModel) { el.geminiModel.value = state.geminiModel; }
  await refreshVoicesUI();
  bindEvents();
}

bootstrap();


