<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Web Media Player & Doc Editor</title>
  <link rel="stylesheet" href="styles/themes.css">
  <link rel="stylesheet" href="styles/base.css">
  <link rel="stylesheet" href="styles/controls.css">
  <link rel="stylesheet" href="styles/playlist-document.css">
  <link rel="stylesheet" href="styles/ssml-editor.css">
  <link rel="stylesheet" href="styles/verification.css">
  <link rel="stylesheet" href="styles/ai-voice-creator.css">
  <link rel="stylesheet" href="styles/whisper.css">
  <link rel="stylesheet" href="styles/quote-footnote-inserter.css">
  <link rel="stylesheet" href="styles/pdf-to-text-converter.css">
  <link rel="stylesheet" href="styles/abbreviated-text-expander.css">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <!-- Defer or lazy-load heavy libs inside modules as needed. -->
  <script defer src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js"></script>
  <!-- Removed html-docx-js (incompatible with mammoth and unused) -->
  <script defer src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
  <script defer src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
</head>

<body>
  <div id="app-root">
    <div class="app-header">
      <div class="app-brand">Roman Catholic Audiobooks</div>
      <button id="audiobook-verification-btn" class="header-nav-button">Audiobook Verification</button>
      <button id="audiobook-text-editor-btn" class="header-nav-button">Audiobook Text Editor</button>
      <button id="ai-voice-creator-btn" class="header-nav-button">AI Voice Creator</button>
      <button id="quote-footnote-inserter-btn" class="header-nav-button">Quote and Footnote Inserter</button>
      <button id="pdf-to-text-converter-btn" class="header-nav-button">PDF to Text Converter</button>
      <button id="abbreviated-text-expander-btn" class="header-nav-button">Abbreviated Text Expander</button>
      <a href="tests/ai-direct-pdf.html" class="header-nav-button" style="text-decoration:none; display:inline-flex; align-items:center;">AI Direct PDF</a>
      <div class="theme-selector-container">
        <label for="theme-select" class="visually-hidden">Select Theme</label>
        <select id="theme-select" aria-label="Select Theme"></select>
      </div>
    </div>

    <!-- Main View for Verification -->
    <div id="app-container" class="app-container">
      <audio id="audio-player"></audio>
      <div class="panel controls-panel" role="region" aria-labelledby="controls-heading">
        <h2 id="controls-heading" class="visually-hidden">Controls</h2>
        <div class="file-loader-section">
          <label for="music-folder-input" class="file-input-label">Open Audiobook Folder</label>
          <input id="music-folder-input" type="file" webkitdirectory directory multiple />
          <label for="doc-folder-input" class="file-input-label">Open Document Folder</label>
          <input id="doc-folder-input" type="file" webkitdirectory directory multiple />
        </div>
        <div class="audio-controls-section">
          <div class="button-group playback-controls">
            <button id="prev-track-btn" aria-label="Previous track"><span class="material-icons">skip_previous</span> Prev</button>
            <button id="play-pause-btn" aria-label="Play"><span class="material-icons">play_arrow</span> Play</button>
            <button id="next-track-btn" aria-label="Next track"><span class="material-icons">skip_next</span> Next</button>
          </div>
          <div class="progress-section">
            <input type="range" id="progress-bar" class="progress-bar" min="0" value="0" />
            <div class="time-display">
              <span id="current-time-display">0:00</span>
              <span id="duration-display">0:00</span>
            </div>
          </div>
          <div class="volume-control">
            <span id="volume-icon" class="material-icons">volume_up</span>
            <input type="range" id="volume-slider" class="volume-slider" min="0" max="1" step="0.01" value="0.5" />
          </div>
        </div>
        <div class="tts-reprocessor-section">
          <h3>Audio Reprocessing</h3>
          <button id="reprocess-g-tts-btn" class="control-button"><span class="material-icons">graphic_eq</span> Reprocess with Google TTS</button>
          <div class="tts-voice-selector">
            <label for="g-tts-voice-select">Select Google Voice:</label>
            <select id="g-tts-voice-select"></select>
            <button id="preview-g-tts-voice-btn" class="control-button" style="margin-left: 8px;">
              <span class="material-icons">volume_up</span> Preview Voice
            </button>
            <label for="g-tts-speed-slider" style="margin-top: 10px;">Speaking Rate:</label>
            <div class="slider-container">
              <input type="range" id="g-tts-speed-slider" min="0.25" max="4.0" step="0.05" value="0.75"><span id="g-tts-speed-value-display">0.75</span>
            </div>
            <div class="config-item" style="margin-top: 8px;">
              <label>
                <input type="checkbox" id="g-tts-chirp-enhanced-toggle" checked />
                Enhanced Narration for Chirp3 HD (prepend audiobook prompt)
              </label>
            </div>

          </div>
          <button id="reprocess-ms-tts-btn" class="control-button" style="margin-top: 10px;"><span class="material-icons">mic</span> Reprocess with Microsoft TTS</button>
          <div class="tts-voice-selector">
            <label for="ms-tts-voice-select">Select Microsoft Voice:</label>
            <select id="ms-tts-voice-select"></select>
            <button id="preview-ms-tts-voice-btn" class="control-button" style="margin-left: 8px;">
              <span class="material-icons">volume_up</span> Preview Voice
            </button>
            <label for="ms-tts-speed-slider" style="margin-top: 10px;">Speaking Rate:</label>
            <div class="slider-container">
              <input type="range" id="ms-tts-speed-slider" min="0.25" max="4.0" step="0.05" value="1.0"><span id="ms-tts-speed-value-display">1.00</span>
            </div>
          </div>
          <div class="tts-save-section" style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #ddd;">
            <button id="save-tts-overwrite-btn" class="control-button accent-button" disabled>
              <span class="material-icons">save</span> Save & Overwrite Original
            </button>
            <p style="font-size: 0.9em; color: #666; margin: 5px 0 0 0;">
              Save the TTS-generated audio to replace the original file
            </p>
          </div>
        </div>
        <div class="verification-controls-section">
          <h3>Audiobook AI Verification</h3>
          <button id="start-verification-btn" class="control-button"><span class="material-icons">fact_check</span> Start Verification</button>
          <button id="open-verification-pdf-btn" class="control-button"><span class="material-icons">picture_as_pdf</span> Reference PDF</button>
          <button id="toggle-tts-controls-btn" class="control-button secondary"><span class="material-icons">tune</span> Hide TTS Controls</button>
          <button id="whisper-audio-check-btn" class="control-button accent-button"><span class="material-icons">hearing</span> AI Whisper Audio Check</button>
        </div>
        <div id="verification-process-panel" class="panel" style="display:none; margin-top: 15px;">
          <h3>Verification Workspace</h3>
          <div id="verification-overall-status">Ready.</div>
          <div id="verification-split" style="display: grid; grid-template-columns: 1fr; gap: 12px; align-items: start;">
            <div>
              <h4 style="margin: 6px 0;">Results</h4>
              <ul id="verification-results-list" class="list-box" role="listbox" aria-label="Verification Results"></ul>
              <audio id="verification-preview-audio-player" style="display:none; width: 100%; margin-top: 8px;" controls></audio>
            </div>
          </div>
        </div>
      </div>
      <div id="playlist-doc-split" class="playlist-doc-split" aria-label="Playlist and Documents split view">
        <div class="panel playlist-panel" role="region" aria-labelledby="playlist-heading">
          <div class="playlist-area">
            <h2 id="playlist-heading">Audiobook Playlist</h2>
            <ul id="music-playlist" class="list-box" role="listbox" aria-labelledby="playlist-heading">
              <li id="no-music-message" class="empty-state" role="status" aria-live="polite">No music files loaded.</li>
            </ul>
          </div>
        </div>

        <div id="playlist-doc-resizer" class="playlist-doc-resizer" role="separator" aria-orientation="vertical" aria-label="Resize playlist and documents" tabindex="0"></div>

        <div class="panel document-panel" role="region" aria-labelledby="document-heading">
        <h2 id="document-heading">Documents</h2>
        <div class="document-area">
          <h3 id="doc-list-heading">Document List</h3>
          <ul id="doc-list" class="list-box" role="listbox" aria-labelledby="doc-list-heading" style="max-height: 150px; flex-grow: 0; margin-bottom:10px;">
            <li id="no-docs-message" class="empty-state" role="status" aria-live="polite">No documents loaded.</li>
          </ul>
          <div class="doc-header">
            <h3 id="doc-content-heading">Content: None Selected</h3>
            <div class="doc-controls">
              <button id="toggle-edit-btn" class="control-button"><span class="material-icons">edit</span> Edit Mode</button>
              <button id="save-doc-btn" class="control-button"><span class="material-icons">save</span> Save Document</button>
              <button id="save-to-folder-btn" class="control-button"><span class="material-icons">folder</span> Save to Folder</button>
              <button id="debug-docx-btn" class="control-button"><span class="material-icons">bug_report</span> Debug DOCX</button>
            </div>
          </div>
          <div id="document-viewer-editor" class="document-viewer-editor" style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; align-items: start;">
            <p id="doc-placeholder">Select a document to view its content.</p>
            <textarea id="doc-editor-textarea" style="display:none;"></textarea>
            <div id="doc-html-viewer" style="display:none;"></div>
            <div id="verification-pdf-panel">
              <h4 style="margin: 6px 0;">Reference PDF</h4>
              <div id="verification-pdf-controls" class="button-group" style="margin-bottom: 6px; display:flex; gap:6px; flex-wrap:wrap;">
                <input id="verification-pdf-file-input" type="file" accept="application/pdf" />
                <button id="verification-pdf-prev-btn" class="control-button">Prev</button>
                <button id="verification-pdf-next-btn" class="control-button">Next</button>
                <input id="verification-pdf-page-input" type="number" min="1" value="1" style="width:70px;" />
                <span id="verification-pdf-total-pages"></span>
                <button id="verification-pdf-zoom-in-btn" class="control-button">Zoom +</button>
                <button id="verification-pdf-zoom-out-btn" class="control-button">Zoom -</button>
                <button id="verification-pdf-zoom-reset-btn" class="control-button">Reset</button>
              </div>
              <div id="verification-pdf-viewport" style="position:relative; overflow:auto; border:1px solid var(--border-secondary); background:var(--bg-panel); max-height: 420px;">
                <canvas id="verification-pdf-canvas"></canvas>
                <div id="verification-pdf-textlayer" class="pdf-textlayer"></div>
              </div>
            </div>
          </div>
          <h3>Insert Break Tag <small>(.txt edit mode)</small></h3>
          <div id="break-tag-buttons" class="button-group break-buttons"></div>
        </div>
      </div>
      </div>
    </div>



    <!-- SSML Editor View -->
    <div id="text-editor-view-container" class="ssml-editor-view hidden-view">
      <h2 id="ssml-editor-heading">Audiobook SSML Editor</h2>
      <p id="ssml-editor-file-info">No document loaded.</p>
      <div class="ssml-editor-layout">
        <div class="ssml-controls-panel">
          <label id="ssml-file-input-label" for="ssml-file-input" class="control-button">Load Document for SSML</label>
          <input id="ssml-file-input" type="file" accept=".txt,.docx" class="visually-hidden" />
          <div class="ssml-control-group">
            <h3>SSML Actions</h3>
            <button id="ssml-add-paragraph-btn" class="control-button">Add Paragraph Tag (Ctrl+P)</button>
            <button id="ssml-add-sentence-btn" class="control-button">Add Sentence Tag (Ctrl+S)</button>

            <!-- New pause-wrapping buttons -->
            <button id="ssml-add-pause-05-btn" class="control-button">Add 0.5s Pause</button>
            <button id="ssml-add-pause-10-btn" class="control-button">Add 1.0s Pause</button>
            <button id="ssml-add-pause-15-btn" class="control-button">Add 1.5s Pause</button>

            <button id="ssml-insert-breakpoint-btn" class="control-button">Insert Breakpoint (Ctrl+B)</button>
            <button id="ssml-preview-btn" class="control-button">Preview SSML (Ctrl+R)</button>
          </div>
          <div class="ssml-control-group">
            <h3>Automated Processing</h3>
            <button id="ssml-pause-quote-btn" class="control-button">Auto Pause 'Quote/End quote'</button>
            <button id="ssml-pause-footnote-btn" class="control-button">Auto Pause 'Footnote/End footnote'</button>
            <button id="ssml-break-long-sentence-btn" class="control-button">Auto Break Long Sentences</button>
            <button id="ssml-set-max-length-btn" class="control-button">Set Max Length</button>
            <button id="ssml-set-long-sentence-threshold-btn" class="control-button">Set Long Sentence Threshold</button>
          </div>
          <button id="ssml-save-btn" class="control-button accent-button" disabled>Save SSML Documents</button>
        </div>
        <div class="ssml-editor-main">
          <textarea id="ssml-text-widget" placeholder="Load a document to start editing..."></textarea>
          <div class="ssml-search-replace">
            <input type="text" id="ssml-find-input" placeholder="Find..." />
            <input type="text" id="ssml-replace-input" placeholder="Replace..." />
            <button id="ssml-find-next-btn" class="control-button">Find Next</button>
            <button id="ssml-replace-btn" class="control-button">Replace</button>
            <button id="ssml-replace-all-btn" class="control-button">Replace All</button>
          </div>
        </div>
      </div>
      <div id="ssml-status-bar" class="status-bar">SSML Editor Ready.</div>
    </div>

    <!-- AI Voice Creator View -->
    <div id="ai-voice-creator-view-container" class="ai-voice-creator-view hidden-view">
      <h2>AI Voice Creator</h2>
      <p id="ai-voice-ssml-loaded-file-info">Select an input source to begin.</p>
      <div class="ai-voice-creator-layout">
        <div class="ai-voice-controls-panel">
          <div class="ai-voice-control-group">
            <h3>Processing Mode</h3>
            <div class="radio-group">
              <input type="radio" id="ai-voice-mode-single-radio" name="ai-voice-mode" value="single" checked><label for="ai-voice-mode-single-radio">Single</label>
              <input type="radio" id="ai-voice-mode-folder-radio" name="ai-voice-mode" value="folder"><label for="ai-voice-mode-folder-radio">Batch</label>
            </div>
          </div>
          <div class="ai-voice-control-group">
            <h3>Input Source</h3>
            <div id="ai-voice-single-input-controls">
              <label id="ai-voice-ssml-file-input-label" for="ai-voice-ssml-file-input" class="control-button">Load Single Document</label>
              <input id="ai-voice-ssml-file-input" type="file" accept=".txt,.docx" class="visually-hidden" />
              <button id="ai-voice-load-from-editor-btn" class="control-button">Load from SSML Editor</button>
            </div>
            <div id="ai-voice-folder-input-controls" style="display: none;">
              <label id="ai-voice-ssml-folder-input-label" for="ai-voice-ssml-folder-input" class="control-button">Load Document Folder</label>
              <input id="ai-voice-ssml-folder-input" type="file" webkitdirectory directory multiple />
            </div>
          </div>
          <div class="ai-voice-control-group">
            <h3>Utilities</h3>
            <button id="ai-voice-export-clean-text-btn" class="control-button">Export Clean Text Files</button>
          </div>
            <div class="config-item" style="margin-top: 8px;">
              <label>
                <input type="checkbox" id="ai-voice-chirp-enhanced-toggle" checked />
                Enhanced Narration for Chirp3 HD (prepend audiobook prompt)
              </label>
            </div>


          <div class="ai-voice-control-group">
            <h3>TTS Configuration</h3>
            <label for="ai-voice-tts-provider-select">TTS Provider:</label>
            <select id="ai-voice-tts-provider-select"><option value="google">Google</option><option value="microsoft">Microsoft</option></select>
            <label for="ai-voice-tts-voice-select" style="margin-top: 10px;">Voice:</label>
            <select id="ai-voice-tts-voice-select"></select>
            <label for="ai-voice-speed-slider" style="margin-top: 10px;">Speaking Rate:</label>
            <div class="slider-container">
              <input type="range" id="ai-voice-speed-slider" min="0.25" max="4.0" step="0.05" value="1.0"><span id="ai-voice-speed-value-display">1.00</span>
            </div>
          </div>
          <div class="ai-voice-control-group">
            <h3>Output Settings</h3>
            <label for="ai-voice-encoding-select">Audio Format:</label>
            <select id="ai-voice-encoding-select"></select>
            <label for="ai-voice-filename-prefix" style="margin-top: 10px;">Filename Prefix (Optional):</label>
            <input type="text" id="ai-voice-filename-prefix" placeholder="e.g., MyAudio_">
          </div>
          <button id="ai-voice-synthesize-btn" class="control-button accent-button"><span class="material-icons">auto_fix_high</span> Synthesize</button>
        </div>
        <div class="ai-voice-main-panel">
          <div id="ai-voice-textarea-container" class="ai-voice-textarea-wrapper">
            <textarea id="ai-voice-ssml-textarea" placeholder="Load or paste text for 'Single' mode..."></textarea>
          </div>
          <div class="ai-voice-status-log-wrapper">
            <h3>Status Log</h3>
            <div id="ai-voice-status-log" class="status-log"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quote and Footnote Inserter View -->
    <div id="quote-footnote-inserter-view-container" class="quote-footnote-inserter-view hidden-view">
      <h2>Quote and Footnote Inserter</h2>
      <p id="quote-footnote-file-info">No document loaded.</p>
      <div class="quote-footnote-layout">
        <div class="quote-footnote-controls-panel">
          <div class="quote-footnote-control-group">
            <h3>Document</h3>
            <label id="quote-footnote-file-input-label" for="quote-footnote-file-input" class="control-button">Open Word Document</label>
            <input id="quote-footnote-file-input" type="file" accept=".docx" class="visually-hidden" />
            <button id="quote-footnote-save-btn" class="control-button accent-button" disabled>Save As</button>
          </div>
          <div class="quote-footnote-control-group">
            <h3>Actions</h3>
            <button id="quote-footnote-add-quote-markers-btn" class="control-button" disabled>Add Quote Markers</button>
            <button id="quote-footnote-add-footnote-markers-btn" class="control-button" disabled>Add Footnote Markers</button>
          </div>
        </div>
        <div class="quote-footnote-main-panel">
          <div class="quote-footnote-text-area-wrapper">
            <textarea id="quote-footnote-text-area" placeholder="Open a Word document to start editing..."></textarea>
          </div>
          <div class="quote-footnote-annotations-wrapper">
            <h3>Extracted Annotations</h3>
            <div id="quote-footnote-annotations-list" class="annotations-list"></div>
          </div>
        </div>
      </div>
      <div id="quote-footnote-status-bar" class="status-bar">Quote and Footnote Inserter Ready.</div>
    </div>

    <!-- PDF to Text Converter View -->
    <div id="pdf-to-text-converter-view-container" class="pdf-to-text-converter-view hidden-view">
      <h2>PDF to Text Converter</h2>
      <p id="pdf-to-text-file-info">No PDF loaded.</p>
      <div class="pdf-to-text-layout">
        <div class="pdf-to-text-controls-panel">
          <div class="pdf-to-text-control-group">
            <h3>OCR Engine</h3>
            <div class="config-item">
              <label for="ocr-engine-select">Select OCR Engine:</label>
              <select id="ocr-engine-select">
                <option value="ollama" selected>Ollama Vision (Local, No Auth)</option>
                <option value="azure">Azure Document Intelligence (Cloud)</option>
                <option value="mistral">Mistral Vision (Local)</option>
              </select>
            </div>
          </div>

          <!-- Azure Configuration -->
          <div id="azure-config-group" class="pdf-to-text-control-group">
            <h3>Azure Configuration</h3>
            <div class="config-item">
              <label for="azure-di-endpoint">Azure DI Endpoint:</label>
              <input type="text" id="azure-di-endpoint" placeholder="https://your-resource.cognitiveservices.azure.com/" />
            </div>
            <div class="config-item">
              <label for="azure-di-key">Azure DI Key:</label>
              <input type="password" id="azure-di-key" placeholder="Your Azure Document Intelligence Key" />
            </div>
            <button id="test-azure-connection-btn" class="control-button">Test Connection</button>
          </div>

          <!-- Mistral Configuration -->
          <div id="mistral-config-group" class="pdf-to-text-control-group" style="display: none;">
            <h3>Mistral Configuration</h3>
            <div id="mistral-auto-start-status" class="auto-start-status" style="display: none;">
              <div class="status-indicator">
                <span class="status-icon">🚀</span>
                <span class="status-text">Auto-starting Mistral server...</span>
              </div>
            </div>
            <div class="config-item">
              <label for="mistral-url">Mistral Server URL:</label>
              <input type="text" id="mistral-url" placeholder="http://localhost:8000" value="http://localhost:8000" />
            </div>
            <div class="config-item">
              <label for="mistral-api-key">Mistral API Key (if required):</label>
              <input type="password" id="mistral-api-key" placeholder="Optional API key" />
            </div>
            <div class="config-item">
              <label for="huggingface-token">HuggingFace Token (for real Mistral):</label>
              <input type="password" id="huggingface-token" placeholder="hf_... (required for real AI)" />
              <small>Get token from <a href="https://huggingface.co/settings/tokens" target="_blank">HuggingFace Settings</a></small>
            </div>
            <div class="config-item">
              <label for="mistral-model">Mistral Model:</label>
              <select id="mistral-model">
                <option value="mistral-large-latest">Mistral Large (Latest)</option>
                <option value="mistral-medium-latest">Mistral Medium (Latest)</option>
                <option value="mistral-small-latest">Mistral Small (Latest)</option>
              </select>
            </div>
            <div class="button-group">
              <button id="test-mistral-connection-btn" class="control-button">Test Connection</button>
              <button id="setup-real-mistral-btn" class="control-button secondary">Setup Real Mistral</button>
              <button id="check-requirements-btn" class="control-button secondary">Check Requirements</button>
            </div>
          </div>
          <div id="ollama-config-group" class="pdf-to-text-control-group">
            <h3>Ollama Configuration</h3>
            <div id="ollama-auto-start-status" class="auto-start-status">Checking Ollama status...</div>
            <div class="config-item">
              <label for="ollama-url">Ollama URL:</label>
              <input type="text" id="ollama-url" value="http://localhost:11434" placeholder="http://localhost:11434" />
            </div>
            <div class="config-item">
              <label for="ollama-model">Vision Model:</label>
              <select id="ollama-model">
                <option value="llava:7b" selected>LLaVA 7B (Best OCR - ~3-4s/page) 🎯</option>
                <option value="moondream:1.8b">Moondream 1.8B (Fast but may describe images - ~1-2s/page) ⚡</option>
                <option value="llava:13b">LLaVA 13B (Higher Quality - ~5-7s/page)</option>
                <option value="llava:34b">LLaVA 34B (Best Quality - ~10-15s/page)</option>
                <option value="bakllava:7b">BakLLaVA 7B (Alternative - ~3-4s/page)</option>
              </select>
            </div>
            <div class="config-item">
              <label>
                <input type="checkbox" id="ollama-fast-mode" checked />
                Fast Mode (shorter prompts, optimized settings)
              </label>
            </div>
            <div class="config-buttons">
              <button id="test-ollama-connection-btn" class="control-button">Test Connection</button>
              <button id="setup-ollama-btn" class="control-button secondary">Setup Ollama</button>
            </div>
          </div>
          <div class="pdf-to-text-control-group">
            <h3>PDF File</h3>
            <label id="pdf-file-input-label" for="pdf-file-input" class="control-button">Select PDF File</label>
            <input id="pdf-file-input" type="file" accept=".pdf" class="visually-hidden" />
            <div id="pdf-file-details" class="file-details"></div>
          </div>
          <div class="pdf-to-text-control-group">
            <h3>Processing Options</h3>
            <div class="config-item">
              <label for="image-dpi">Image DPI:</label>
              <select id="image-dpi">
                <option value="150">150 DPI (Fast)</option>
                <option value="300" selected>300 DPI (Recommended)</option>
                <option value="600">600 DPI (High Quality)</option>
              </select>
            </div>
            <div class="config-item">
              <label>
                <input type="checkbox" id="save-images" checked />
                Save page images
              </label>
            </div>
          </div>
          <div class="pdf-to-text-control-group">
            <h3>Actions</h3>
            <button id="start-conversion-btn" class="control-button accent-button" disabled>
              <span class="material-icons">auto_fix_high</span> Start Conversion
            </button>
            <button id="download-result-btn" class="control-button" disabled>
              <span class="material-icons">download</span> Download Word Document
            </button>
          </div>
        </div>
        <div class="pdf-to-text-main-panel">
          <div class="pdf-preview-wrapper">
            <h3>PDF Preview</h3>
            <div id="pdf-preview-container" class="pdf-preview">
              <p>Select a PDF file to see preview...</p>
            </div>
          </div>
          <div class="conversion-progress-wrapper">
            <h3>Conversion Progress</h3>
            <div id="conversion-progress" class="progress-container">
              <div class="progress-bar-container">
                <div id="pdf-progress-bar" class="progress-bar"></div>
                <span id="pdf-progress-text">Ready to start</span>
              </div>
              <div id="conversion-log" class="conversion-log"></div>
            </div>
          </div>
        </div>
      </div>
      <div id="pdf-to-text-status-bar" class="status-bar">PDF to Text Converter Ready.</div>
    </div>

    <!-- Abbreviated Text Expander View -->
    <div id="abbreviated-text-expander-view-container" class="abbreviated-text-expander-view hidden-view">
      <h2>Abbreviated Text Expander</h2>
      <p id="abbreviated-text-expander-file-info">No document loaded.</p>
      <div class="abbreviated-text-expander-layout">
        <!-- Mobile Toggle Button -->
        <button id="mobile-config-toggle" class="mobile-config-toggle" style="display: none;">
          <span class="toggle-icon">⚙️</span>
          <span class="toggle-text">Tools</span>
        </button>

        <div class="abbreviated-text-expander-config-panel" id="abbreviated-text-expander-config-panel">
          <div class="config-section">
            <h3>Google Gemini AI Model</h3>
            <div class="config-item">
              <label for="gemini-model-select">AI Model:</label>
              <select id="gemini-model-select">
                <option value="gemini-2.5-flash" selected>Gemini 2.5 Flash ⚡ (Recommended)</option>
                <option value="gemini-2.5-pro">Gemini 2.5 Pro 🧠 (Most Powerful)</option>
                <option value="gemini-2.0-flash">Gemini 2.0 Flash (Fast)</option>
                <option value="gemini-1.5-flash">Gemini 1.5 Flash (Legacy)</option>
                <option value="gemini-1.5-pro">Gemini 1.5 Pro (Legacy)</option>
              </select>
              <div class="model-info">
                <p><strong>Gemini 2.5</strong> models feature advanced thinking capabilities for better reasoning and accuracy.</p>
              </div>
            </div>
          </div>


          <!-- Document Operations -->
          <div class="config-section">
            <h3>Document</h3>
            <div class="config-item">
              <label id="abbreviated-text-expander-file-input-label" for="abbreviated-text-expander-file-input" class="control-button">Open Word Document</label>
              <input id="abbreviated-text-expander-file-input" type="file" accept=".docx" class="visually-hidden" />
              <button id="abbreviated-text-expander-save-btn" class="control-button accent-button" disabled>Save Document</button>
            </div>
          </div>

          <!-- Master Formatting -->
          <div class="config-section">
            <h3>Master Formatting</h3>
            <div class="config-item">
              <button id="run-all-formatting-btn" class="control-button master-button" disabled>🚀 Run All Formatting</button>
              <button id="cancel-run-all-btn" class="control-button secondary small" style="display: none;">Cancel</button>
            </div>
            <div class="config-item">
              <button id="select-all-text-btn" class="control-button secondary" disabled>Select All</button>
              <button id="clear-selection-btn" class="control-button secondary" disabled>Clear Selection</button>
            </div>
            <div class="config-item">
              <button id="undo-changes-btn" class="control-button secondary" disabled>Undo</button>
              <button id="redo-changes-btn" class="control-button secondary" disabled>Redo</button>
            </div>
            <!-- Progress Indicator -->
            <div id="run-all-progress" class="progress-indicator" style="display: none;">
              <div class="progress-bar">
                <div class="progress-fill"></div>
              </div>
              <div class="progress-text">Preparing...</div>
            </div>
          </div>

          <!-- AI Text Formatting -->
          <div class="config-section">
            <h3>AI Text Formatting</h3>
            <div class="config-item">
              <button id="expand-abbreviated-text-btn" class="control-button accent-button" disabled>Expand Abbreviated Text</button>
              <button id="expand-abbreviations-btn" class="control-button accent-button" disabled>Expand All Abbreviations</button>
            </div>
            <div class="config-item">
              <button id="format-scripture-btn" class="control-button accent-button" disabled>Format Scripture References</button>
              <button id="format-quotes-btn" class="control-button accent-button" disabled>Add Quote End Quote</button>
            </div>
            <div class="config-item">
              <button id="format-papal-saints-btn" class="control-button accent-button" disabled>Format Papal & Saint Names</button>
              <button id="format-footnotes-btn" class="control-button accent-button" disabled>Format Footnotes for Audiobook</button>
            </div>
          </div>

        </div>
        <div class="abbreviated-text-expander-main-panel">
          <div class="abbreviated-split" id="abbreviated-split">
            <div class="abbreviated-text-expander-document-wrapper">
              <div class="document-header">
                <div class="document-header-top">
                  <h3>Document Content</h3>
                  <div class="document-stats">
                    <span id="document-word-count">0 words</span>
                    <span id="document-char-count">0 characters</span>
                  </div>
                </div>
              </div>
              <div id="abbreviated-text-expander-document-container" class="document-container">
                <div id="abbreviated-text-expander-text-area" contenteditable="true" class="document-editor" data-placeholder="Open a Word document to start editing..."></div>
                <div class="document-overlay" id="document-overlay" style="display: none;">
                  <div class="loading-spinner"></div>
                  <p>Loading document...</p>
                </div>
              </div>
            </div>

            <div id="abbrev-split-resizer" class="split-resizer" role="separator" aria-orientation="vertical" aria-label="Resize editor and PDF" tabindex="0"></div>

            <div class="pdf-viewer-wrapper" id="abbrev-pdf-viewer-wrapper">
              <div class="pdf-header">
                <h3>Reference PDF</h3>
                <div class="pdf-file-controls">
                  <label id="abbrev-pdf-file-input-label" for="abbrev-pdf-file-input" class="control-button info-button"><span class="material-icons">attach_file</span> Attach PDF</label>
                  <input id="abbrev-pdf-file-input" type="file" accept=".pdf" class="visually-hidden" />
                  <span id="abbrev-pdf-filename" class="pdf-filename">No PDF attached</span>
                </div>
              </div>
              <div class="pdf-controls">
                <button id="abbrev-pdf-prev-btn" class="control-button" title="Previous page"><span class="material-icons">chevron_left</span></button>
                <div class="pdf-page-indicator">
                  <input id="abbrev-pdf-page-input" type="number" min="1" value="1" />
                  <span>/ <span id="abbrev-pdf-total-pages">1</span></span>
                </div>
                <button id="abbrev-pdf-next-btn" class="control-button" title="Next page"><span class="material-icons">chevron_right</span></button>

                <div class="pdf-zoom-controls">
                  <button id="abbrev-pdf-zoom-out-btn" class="control-button" title="Zoom out"><span class="material-icons">zoom_out</span></button>
                  <button id="abbrev-pdf-zoom-reset-btn" class="control-button" title="Reset zoom"><span class="material-icons">center_focus_strong</span></button>
                  <button id="abbrev-pdf-zoom-in-btn" class="control-button" title="Zoom in"><span class="material-icons">zoom_in</span></button>
                </div>
                <div id="abbrev-pdf-search-indicator" class="pdf-search-indicator" aria-live="polite" style="display:none;">
                  <span class="spinner"></span>
                  <span class="text">Searching…</span>
                </div>
              </div>
              <div class="pdf-viewport" id="abbrev-pdf-viewport">
                <div id="abbrev-pdf-empty" class="pdf-empty">Attach a PDF to preview</div>
                <canvas id="abbrev-pdf-canvas"></canvas>
                <div id="abbrev-pdf-textlayer" class="pdf-textlayer" aria-hidden="true"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="abbreviated-text-expander-status-bar" class="status-bar">Abbreviated Text Expander Ready.</div>
    </div>

    <!-- Text Expansion Preview Modal -->
    <div id="text-expansion-preview-modal" class="ssml-modal" style="display: none;">
      <div class="ssml-modal-content large">
        <h3>Text Expansion Preview</h3>
        <div class="expansion-preview-content">
          <div class="original-text-section">
            <h4>Original Text:</h4>
            <div id="original-text-display" class="text-display original"></div>
          </div>
          <div class="expanded-text-section">
            <h4>Expanded Text:</h4>
            <div id="expanded-text-display" class="text-display expanded" contenteditable="true"></div>
          </div>
          <div class="preview-controls">
            <button id="accept-expansion-btn" class="control-button accent-button">Accept Changes</button>

            <button id="reject-expansion-btn" class="control-button secondary">Reject Changes</button>
            <button id="close-preview-btn" class="control-button">Close</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <div id="view-ssml-text-modal" class="ssml-modal" style="display: none;">
      <div class="ssml-modal-content large">
        <h3 id="view-ssml-text-modal-title">View SSML Text</h3>
        <div id="view-ssml-modal-content-area" class="document-viewer-editor"><pre></pre></div>
        <button id="view-ssml-text-modal-close-btn" class="control-button">Close</button>
      </div>
    </div>

    <!-- Whisper Audio Check Modal -->
    <div id="whisper-audio-check-modal" class="ssml-modal" style="display: none;">
      <div class="ssml-modal-content large">
        <h3>AI Whisper Audio Check</h3>
        <div class="whisper-modal-content">
          <div class="whisper-control-group">
            <h4>File Selection</h4>
            <div class="radio-group">
              <input type="radio" id="whisper-single-file-radio" name="whisper-file-mode" value="single" checked>
              <label for="whisper-single-file-radio">Check 1 File</label>
              <input type="radio" id="whisper-all-files-radio" name="whisper-file-mode" value="all">
              <label for="whisper-all-files-radio">Check All Files</label>
            </div>
            <div id="whisper-single-file-controls">
              <label for="whisper-file-select">Select Audio File:</label>
              <select id="whisper-file-select"></select>
            </div>
          </div>
          <div class="whisper-control-group">
            <h4>Whisper Model</h4>
            <label for="whisper-model-select">Model:</label>
            <select id="whisper-model-select">
              <!-- Options populated by JavaScript -->
            </select>

          </div>
          <div class="whisper-control-group">
            <h4>Output</h4>
            <div id="whisper-output-area" class="whisper-output">
              <p>Select files and click "Start Check" to begin transcription.</p>
            </div>
          </div>
          <div class="whisper-modal-actions">
            <button id="whisper-start-check-btn" class="control-button accent-button">
              <span class="material-icons">play_arrow</span> Start Check
            </button>
            <button id="whisper-modal-close-btn" class="control-button">Close</button>
          </div>
        </div>
      </div>
    </div>

    <!-- SSML Settings Modals (top-level) -->
    <div id="ssml-set-max-length-modal" class="ssml-modal" style="display: none;">
      <div class="ssml-modal-content">
        <h3>Set Maximum SSML Chunk Length</h3>
        <p>Maximum characters per SSML part (minimum 1000):</p>
        <input id="ssml-max-length-input" type="number" min="1000" value="1000" style="width:120px;"/>
        <div class="button-group" style="margin-top:10px; display:flex; gap:8px;">
          <button id="ssml-max-length-confirm-btn" class="control-button accent-button">Apply</button>
          <button id="ssml-max-length-cancel-btn" class="control-button">Cancel</button>
        </div>
      </div>
    </div>

    <div id="ssml-set-long-sentence-threshold-modal" class="ssml-modal" style="display: none;">
      <div class="ssml-modal-content">
        <h3>Set Long Sentence Threshold</h3>
        <p>Insert a 500ms break before sentences exceeding this word count (min 5):</p>
        <input id="ssml-long-sentence-threshold-input" type="number" min="5" value="15" style="width:120px;"/>
        <div class="button-group" style="margin-top:10px; display:flex; gap:8px;">
          <button id="ssml-long-sentence-confirm-btn" class="control-button accent-button">Apply</button>
          <button id="ssml-long-sentence-cancel-btn" class="control-button">Cancel</button>
        </div>
      </div>
    </div>


    <div id="status-bar" class="status-bar" role="status">Welcome!</div>
  </div>
  <script type="module" src="index.js"></script>
</body>
</html>