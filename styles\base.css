/* PDF text layer for verification viewer */
.pdf-textlayer {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.pdf-textlayer span {
  position: absolute;
  white-space: pre;
  transform-origin: 0 0;
}
.pdf-textlayer .match-highlight {
  background: rgba(255, 235, 59, 0.6);
}
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    margin: 0;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    display: flex;
    flex-direction:column;
    height: 100vh;
    overflow: hidden;
}

#app-root {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.app-header {
    background-color: var(--bg-header);
    color: var(--text-header);
    padding: 10px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
    box-shadow: 0 2px 4px var(--shadow-medium);
}

.app-brand {
    font-weight: 700;
    letter-spacing: 0.3px;
    margin-right: 6px;
    padding-right: 12px;
    border-right: 1px solid var(--border-primary);
    color: var(--text-header);
    white-space: nowrap;      /* keep text on one line */
    flex-shrink: 0;           /* prevent shrinking that causes wrap */
    min-width: max-content;   /* ensure it sizes to its content */
}

.header-nav-button {
    background-color: var(--button-primary-bg);
    color: var(--text-button-primary);
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.95em;
    transition: background-color 0.2s, box-shadow 0.2s;
}

.header-nav-button:hover {
    background-color: var(--button-primary-hover-bg);
    box-shadow: 0 1px 3px var(--shadow-medium);
}

.header-nav-button.active-nav-button {
    background-color: var(--button-primary-active-bg);
    font-weight: bold;
    box-shadow: inset 0 1px 3px var(--shadow-inset-active-nav);
}

/* Keyboard focus visibility improvements */
.header-nav-button:focus-visible,
.control-button:focus-visible,
.file-input-label:focus-visible,
select:focus-visible,
input[type="text"]:focus-visible,
input[type="password"]:focus-visible,
textarea:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}


.app-container {
    display: flex; 
    flex-grow: 1;
    gap: 15px;
    width: 100%;
    background-color: var(--bg-app-container);
    padding: 15px;
    box-sizing: border-box;
    box-shadow: 0 0 20px var(--shadow-medium);
    overflow: hidden; 
}

/* This single rule will correctly hide any inactive view container */
.hidden-view {
    display: none !important;
}

.panel {
    background-color: var(--bg-panel);
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 5px var(--shadow-light);
    display: flex;
    flex-direction: column; 
}

.status-bar {
    background-color: var(--bg-status-bar);
    color: var(--text-status-bar);
    padding: 8px 15px;
    text-align: center;
    font-size: 0.9em;
    z-index: 1000;
    flex-shrink:0;
    height: 34px;
    box-sizing: border-box;
}

.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

h2, h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--text-panel-heading);
    font-weight: 500;
}

h3 small {
    font-weight: normal;
    color: var(--text-placeholder);
    font-size: 0.8em;
}

.controls-panel h2, .playlist-panel h2, .document-panel h2, #verification-process-panel h2 {
    font-size: 1.3em;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--border-primary);
    padding-bottom: 8px;
    flex-shrink: 0;
}

.controls-panel .tts-reprocessor-section h3,
.document-panel h3 { /* General h3 style within these panels */
    font-size: 1em;
    margin-top: 0;
    flex-shrink: 0;
}