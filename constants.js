/* ------------------------------------------------------------------
 *  constants.js   ── pure values only (no functions)
 * ------------------------------------------------------------------*/

/*──────────────────────────────
  Supported file extensions
──────────────────────────────*/
export const SUPPORTED_AUDIO_EXTENSIONS = ['.mp3', '.wav', '.ogg', '.flac', '.aac'];
export const SUPPORTED_DOC_EXTENSIONS   = ['.txt', '.docx'];

/*──────────────────────────────
  UI helper data
──────────────────────────────*/
export const BREAK_DURATIONS = [
  { label: '0.5s Break', value: 0.5 },
  { label: '1.0s Break', value: 1.0 },
  { label: '2.0s Break', value: 2.0 },
];

/*===================================================================
  GOOGLE CLOUD TEXT-TO-SPEECH
===================================================================*/
export const DEFAULT_G_TTS_VOICE_NAME  = 'en-GB-News-L';
export const G_TTS_SPEECH_SPEED        = 0.75;
export const G_TTS_SAMPLE_RATE_HERTZ   = 44_100;

/* Multi-platform env-var lookup for the Google Cloud TTS API key */
export const G_TTS_API_KEY =
  /* Node / server-side */
  (typeof process !== 'undefined' && process.env && (
    process.env.G_TTS_API_KEY ||
    process.env.API_KEY ||                           // legacy
    process.env.NEXT_PUBLIC_G_TTS_API_KEY            // Next.js client bundle
  )) ||
  /* Vite / import.meta style */
  (typeof import.meta !== 'undefined' && import.meta.env?.VITE_G_TTS_API_KEY) ||
  null;

// Debug logging for Google TTS API key (disable in production to avoid noise)
const __DEBUG_CONSTANTS = true;
if (__DEBUG_CONSTANTS) {
  console.log('Constants: import.meta available:', typeof import.meta !== 'undefined');
  console.log('Constants: import.meta.env available:', typeof import.meta !== 'undefined' && !!import.meta.env);
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    console.log('Constants: All env vars:', Object.keys(import.meta.env));
    console.log('Constants: VITE_G_TTS_API_KEY from env length:', import.meta.env.VITE_G_TTS_API_KEY?.length || 0);
    console.log('Constants: VITE_G_TTS_API_KEY starts with AIza:', import.meta.env.VITE_G_TTS_API_KEY?.startsWith('AIza') || false);
  }
  console.log('Constants: G_TTS_API_KEY final length:', G_TTS_API_KEY?.length || 0);
  console.log('Constants: G_TTS_API_KEY starts with AIza:', G_TTS_API_KEY?.startsWith('AIza') || false);
}



/* Extension → Google audioEncoding map */
export const EXTENSION_TO_ENCODING_GOOGLE = {
  '.wav':  'LINEAR16',
  '.mp3':  'MP3',
  '.ogg':  'OGG_OPUS',
  '.flac': 'FLAC',
  '.aac':  'MP3',   // synth as MP3 for widest support
};

/* Chirp3 HD specialized narration prompt (for audiobook-quality synthesis) */
export const CHIRP3_NARRATION_PROMPT = `You are a professional audiobook narrator with expertise in expressive, clear, and engaging reading.

Your objective is to read the provided book content while maintaining optimal quality, clarity, and listener engagement.

Strict Adherence to Provided Text:
- Exactitude: You must strictly read only what is provided in the text. Do not add, omit, or alter any part of the text.
- No Commentary: Do not include any commentary, explanations, or meta-statements such as "Yes, I will do this" or similar responses. Your output should consist solely of the narration of the given material without additional context or interjections.
- Focus: Avoid any deviation from the content that is provided. The focus must be solely on reading the text as is.

General Reading Style:
- Use a warm, clear, and steady tone.
- Pronounce words accurately, and modulate your voice to reflect the mood, context, and pacing of the text.
- Maintain a comfortable pace that balances comprehension with engagement—neither too fast nor too slow.

Handling Headers and Structural Elements:

Detection:
- Identify headers, chapter titles, section breaks, and other structural cues in the text.
- Look for visual indicators such as lines in all uppercase, larger font sizes, bold formatting, centered alignment, or distinct spacing before and after the header.
- Recognize common header phrases like "Chapter 1", "Section", or other titles that typically begin sections.

Emphasis and Pausing:
- Upon encountering a header, pause for 2 seconds before speaking the header to signal a clear transition.
- Use a slightly distinct tone or intonation to indicate importance, but remain professional and consistent.
- After reading a header, pause again for 2 seconds before proceeding with the main text to give listeners time to assimilate the new section.

Timing and Pacing:

Sentence-Level Pacing:
- For longer sentences, use natural inflection and slight pauses at commas, semicolons, and periods to enhance comprehension.
- For complex passages, vary your speed slightly to emphasize key points or to allow the listener time to absorb detailed information.

Paragraph and Section Transitions:
- At the end of paragraphs, insert a subtle pause (around 1 second) to mark the transition without breaking the narrative flow.
- For scene changes or significant breaks (if not marked by headers), consider extending pauses up to 2 seconds to signal a change in context or setting.

Voice Modulation and Emphasis:
- Adjust intonation based on the content:
  - Use a confident and calm tone for factual or informative sections.
  - Apply gentle variation in tone for dialogue, character voices, or dramatic moments without deviating from a professional style.
  - Emphasize keywords or phrases when necessary to maintain listener interest and convey significance.`;


/*===================================================================
  MICROSOFT AZURE TEXT-TO-SPEECH
===================================================================*/
export const DEFAULT_MS_TTS_VOICE_NAME = 'en-US-AvaMultilingualNeural';
export const MS_TTS_SPEECH_SPEED       = 'default';        // 'slow'|'medium'|'fast' or %
export const MS_TTS_SAMPLE_RATE_HERTZ  = 'audio-24khz-160kbitrate-mono-mp3';

/* Env-var lookup that works for Node, Next.js (NEXT_PUBLIC_), and Vite (VITE_) */
export const MS_TTS_API_KEY =
  (typeof process !== 'undefined' && process.env && (
    process.env.MS_TTS_API_KEY ||
    process.env.NEXT_PUBLIC_MS_TTS_API_KEY
  )) ||
  (typeof import.meta !== 'undefined' && import.meta.env?.VITE_MS_TTS_API_KEY) ||
  null;

export const MS_TTS_SERVICE_REGION =
  (typeof process !== 'undefined' && process.env && (
    process.env.MS_TTS_SERVICE_REGION ||
    process.env.NEXT_PUBLIC_MS_TTS_SERVICE_REGION
  )) ||
  (typeof import.meta !== 'undefined' && import.meta.env?.VITE_MS_TTS_SERVICE_REGION) ||
  null;

/*===================================================================
  GOOGLE GEMINI AI CONFIGURATION
===================================================================*/
// Available Gemini models (latest 2.5 family)
export const GEMINI_MODELS = {
    'gemini-2.5-flash': {
        name: 'Gemini 2.5 Flash ⚡',
        description: 'Latest fast model with adaptive thinking - best price-performance',
        url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent'
    },
    'gemini-2.5-pro': {
        name: 'Gemini 2.5 Pro 🧠',
        description: 'Most powerful thinking model with maximum accuracy',
        url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent'
    },
    'gemini-2.0-flash': {
        name: 'Gemini 2.0 Flash',
        description: 'Next-gen features with superior speed',
        url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent'
    },
    'gemini-1.5-flash': {
        name: 'Gemini 1.5 Flash (Legacy)',
        description: 'Previous generation fast model',
        url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent'
    },
    'gemini-1.5-pro': {
        name: 'Gemini 1.5 Pro (Legacy)',
        description: 'Previous generation capable model',
        url: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent'
    }
};

// Default model - using the latest and best price-performance
export const DEFAULT_GEMINI_MODEL = 'gemini-2.5-flash';
export const GEMINI_API_BASE_URL = GEMINI_MODELS[DEFAULT_GEMINI_MODEL].url;

/* Env-var lookup for Google Gemini API key */
export const GEMINI_API_KEY =
  (typeof process !== 'undefined' && process.env && (
    process.env.GEMINI_API_KEY ||
    process.env.NEXT_PUBLIC_GEMINI_API_KEY
  )) ||
  (typeof import.meta !== 'undefined' && import.meta.env?.VITE_GEMINI_API_KEY) ||
  'AIzaSyCbnYvywkVSuFv87CsDDzlAFW6Rw9zgYD4'; // Default API key for abbreviated text expander

// Debug logging for Gemini API key
console.log('Constants: GEMINI_API_KEY final length:', GEMINI_API_KEY?.length || 0);
console.log('Constants: GEMINI_API_KEY starts with AIza:', GEMINI_API_KEY?.startsWith('AIza') || false);

/*===================================================================
  LOCAL WHISPER CONFIGURATION
===================================================================*/
// Available local Whisper models (ordered by size/quality)
export const WHISPER_MODELS = [
  { value: 'tiny', name: 'tiny (39 MB, fastest, lowest quality)' },
  { value: 'base', name: 'base (74 MB, fast, good quality)' },
  { value: 'small', name: 'small (244 MB, balanced)' },
  { value: 'medium', name: 'medium (769 MB, good quality)' },
  { value: 'large', name: 'large (1550 MB, best quality)' },
  { value: 'large-v2', name: 'large-v2 (1550 MB, improved large)' },
  { value: 'large-v3', name: 'large-v3 (1550 MB, latest large)' }
];

// Default model for new users (good balance of speed and quality)
export const DEFAULT_WHISPER_MODEL = 'base';

// Whisper processing endpoint (local Python server)
export const WHISPER_ENDPOINT = 'http://localhost:8001/transcribe';



/*===================================================================
  AI VOICE CREATOR ENCODINGS - Updated: 2025-06-07 13:19
===================================================================*/
export const AI_VOICE_CREATOR_ENCODINGS = {
  'mp3': {
    extension: '.mp3',
    apiValue: 'MP3',
    mimeType: 'audio/mpeg'
  },
  'wav': {
    extension: '.wav',
    apiValue: 'LINEAR16',
    mimeType: 'audio/wav'
  },
  'ogg': {
    extension: '.ogg',
    apiValue: 'OGG_OPUS',
    mimeType: 'audio/ogg'
  },
  'flac': {
    extension: '.flac',
    apiValue: 'FLAC',
    mimeType: 'audio/flac'
  }
};
