
// Cache Bust: 2024-12-19-14:30:15 - downloadBlob import fix
import * as dom from '../domElements.js';
import * as state from '../state.js';
import * as ttsService from './ttsService.js';
import { G_TTS_API_KEY, MS_TTS_API_KEY, MS_TTS_SERVICE_REGION, AI_VOICE_CREATOR_ENCODINGS, DEFAULT_G_TTS_VOICE_NAME, DEFAULT_MS_TTS_VOICE_NAME, SUPPORTED_DOC_EXTENSIONS } from '../constants.js';
import { downloadBlob, ssmlEscapeXmlEntities, naturalSort } from '../utils.js';

const SSML_BREAKPOINT_MARKER = "<!--BREAKPOINT-->";
let currentLoadedFile = null; // For single file mode
let batchFilesToProcess = []; // For folder mode
let currentProcessingMode = 'single'; // 'single' or 'folder'
let currentTTSProvider = 'google'; // 'google' or 'microsoft'

// Hold references to UI functions passed from index.js
let uiUpdateStatusMain = () => {}; // For the main status bar
let uiPopulateGoogleVoiceSelectorFunction = () => {};
let uiPopulateMicrosoftVoiceSelectorFunction = () => {};

function populateEncodingSelector(selectElement, defaultEncoding = 'mp3') {
    if (!selectElement) return;
    selectElement.innerHTML = '';

    Object.keys(AI_VOICE_CREATOR_ENCODINGS).forEach(encodingKey => {
        const encoding = AI_VOICE_CREATOR_ENCODINGS[encodingKey];
        const option = document.createElement('option');
        option.value = encodingKey;
        option.textContent = `${encoding.extension.toUpperCase()} (${encoding.mimeType})`;
        if (encodingKey === defaultEncoding) {
            option.selected = true;
        }
        selectElement.appendChild(option);
    });
}


function logStatus(message, type = 'info') {
    if (!dom.aiVoiceStatusLog) return;
    const p = document.createElement('p');
    p.textContent = message;
    if (type === 'error') p.classList.add('error');
    if (type === 'success') p.classList.add('success');

    const maxLogEntries = 100;
    while (dom.aiVoiceStatusLog.childNodes.length >= maxLogEntries) {
        dom.aiVoiceStatusLog.removeChild(dom.aiVoiceStatusLog.firstChild);
    }
    dom.aiVoiceStatusLog.appendChild(p);
    dom.aiVoiceStatusLog.scrollTop = dom.aiVoiceStatusLog.scrollHeight; // Auto-scroll
}

function updateUIVisibilityForMode(mode) {
    currentProcessingMode = mode;
    if (mode === 'single') {
        dom.aiVoiceSingleInputControls.style.display = 'block';
        dom.aiVoiceFolderInputControls.style.display = 'none';
        dom.aiVoiceTextareaContainer.style.display = 'flex';
        if (currentLoadedFile) {
            dom.aiVoiceSSMLLoadedFileInfo.textContent = `File: ${currentLoadedFile.name}`;
        } else if (dom.aiVoiceSSMLTextarea.value.trim() !== "") {
            dom.aiVoiceSSMLLoadedFileInfo.textContent = `Using content from SSML Editor or pasted input.`;
        } else {
            dom.aiVoiceSSMLLoadedFileInfo.textContent = `No SSML loaded for single input.`;
        }
    } else { // folder mode
        dom.aiVoiceSingleInputControls.style.display = 'none';
        dom.aiVoiceFolderInputControls.style.display = 'block';
        dom.aiVoiceTextareaContainer.style.display = 'none';
        if (batchFilesToProcess.length > 0) {
            dom.aiVoiceSSMLLoadedFileInfo.textContent = `${batchFilesToProcess.length} file(s) selected for batch processing.`;
        } else {
            dom.aiVoiceSSMLLoadedFileInfo.textContent = `No folder selected for batch processing.`;
            // Small enhancement: auto-open the folder picker when switching to Batch mode
            if (dom.aiVoiceSSMLFolderInput) {
                setTimeout(() => {
                    try {
                        dom.aiVoiceSSMLFolderInput.click();
                        logStatus('Select a folder with .txt or .docx files for batch processing...');
                    } catch (err) {
                        // Fallback: focus input if programmatic click is blocked
                        try {
                            dom.aiVoiceSSMLFolderInput.focus();
                            dom.aiVoiceSSMLFolderInput.style.outline = '2px solid var(--border-primary)';
                        } catch {}
                    }
                }, 50);
            }
        }
    }
    // Enable/disable utilities based on mode and selection
    if (dom.aiVoiceExportCleanTextBtn) {
        dom.aiVoiceExportCleanTextBtn.disabled = !(mode === 'folder' && batchFilesToProcess.length > 0);
    }
    if (mode === 'single') {
        batchFilesToProcess = [];
    } else {
        currentLoadedFile = null;
    }
}

async function readFileContent(fileObject) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = async (e) => {
            let rawContent = "";
            try {
                if (fileObject.name.endsWith('.txt')) {
                    rawContent = e.target.result;
                } else if (fileObject.name.endsWith('.docx')) {
                    if (window.mammoth) {
                        const result = await window.mammoth.extractRawText({ arrayBuffer: e.target.result });
                        rawContent = result.value;
                    } else {
                        throw new Error("Mammoth.js library not available for DOCX processing.");
                    }
                }
                resolve(rawContent);
            } catch (err) {
                reject(err);
            }
        };
        reader.onerror = () => {
            reject(new Error(`Error reading file: ${fileObject.name}`));
        };

        if (fileObject.name.endsWith('.txt')) {
            reader.readAsText(fileObject);
        } else if (fileObject.name.endsWith('.docx')) {
            reader.readAsArrayBuffer(fileObject);
        } else {
            reject(new Error(`Unsupported file type for reading content: ${fileObject.name}`));
        }
    });
}


async function handleSSMLFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    currentLoadedFile = file;
    batchFilesToProcess = [];
    updateUIVisibilityForMode('single');

    dom.aiVoiceSSMLLoadedFileInfo.textContent = `File: ${file.name}`;
    logStatus(`Loading SSML from "${file.name}"...`);

    const extension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
    if (!SUPPORTED_DOC_EXTENSIONS.includes(extension)) {
        logStatus(`Unsupported file type: ${extension}. Please select .txt or .docx.`, 'error');
        dom.aiVoiceSSMLFileInput.value = '';
        dom.aiVoiceSSMLLoadedFileInfo.textContent = `Error: Unsupported file type.`;
        currentLoadedFile = null;
        return;
    }

    try {
        const rawContent = await readFileContent(file);
        dom.aiVoiceSSMLTextarea.value = rawContent;
        logStatus(`Successfully loaded SSML from "${file.name}".`);
    } catch (err) {
        console.error('Error processing SSML file:', err);
        logStatus(`Error loading SSML: ${err.message}`, 'error');
        dom.aiVoiceSSMLLoadedFileInfo.textContent = `Error loading: ${file.name}`;
        currentLoadedFile = null;
    }
    dom.aiVoiceSSMLFileInput.value = '';
}

async function handleSSMLFolderUpload(event) {
    const files = event.target.files;
    if (!files || files.length === 0) {
        logStatus("No folder or files selected.", "error");
        return;
    }

    currentLoadedFile = null;
    dom.aiVoiceSSMLTextarea.value = "";

    const validFiles = Array.from(files).filter(file => {
        const extension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();


        return SUPPORTED_DOC_EXTENSIONS.includes(extension);
    });

    if (validFiles.length === 0) {
        logStatus("No supported .txt or .docx files found in the selected folder.", "error");
        dom.aiVoiceSSMLLoadedFileInfo.textContent = "No compatible files in folder.";
        batchFilesToProcess = [];
        updateUIVisibilityForMode('folder');
        return;
    }

    validFiles.sort((a, b) => naturalSort(a.name, b.name));

    batchFilesToProcess = validFiles;
    logStatus(`Loaded ${batchFilesToProcess.length} file(s) for batch processing.`);
    updateUIVisibilityForMode('folder');

    dom.aiVoiceSSMLFolderInput.value = '';
}


function handleLoadFromEditor() {
    if (currentProcessingMode !== 'single') {
        logStatus("Switch to 'Single Input' mode to load from SSML Editor.", "error");
        return;
    }
    if (dom.ssmlTextWidget && dom.aiVoiceSSMLTextarea) {
        const editorContent = dom.ssmlTextWidget.value;
        if (editorContent.trim() === "") {
            logStatus("SSML Editor is empty. Nothing to load.", "error");
            return;
        }
        dom.aiVoiceSSMLTextarea.value = editorContent;
        currentLoadedFile = state.ssmlEditorFile;
        batchFilesToProcess = [];
        if (currentLoadedFile) {
             dom.aiVoiceSSMLLoadedFileInfo.textContent = `From Editor: ${currentLoadedFile.name}`;
        } else {
            dom.aiVoiceSSMLLoadedFileInfo.textContent = `From Editor (unsaved content)`;
        }
        logStatus("Loaded content from SSML Editor.");
    } else {
        logStatus("Could not access SSML Editor content.", "error");
    }
}


function prepareSSMLForSynthesis(rawTextChunk, rate) {
    let ssmlContent = rawTextChunk.trim();
    const hasSpeakTag = /<speak[\s>]/.test(ssmlContent);

    if (!hasSpeakTag) {
        ssmlContent = ssmlEscapeXmlEntities(ssmlContent);
        ssmlContent = `<speak><prosody rate="${rate}">${ssmlContent}</prosody></speak>`;
    } else {
        if (rate !== 1.0) { // Only attempt to inject/modify prosody if rate is not default
            const prosodyRegex = /<prosody[^>]*rate="([^"]*)"[^>]*>/i;
            const speakContentRegex = /(<speak[^>]*>)([\s\S]*?)(<\/speak>)/i;
            let match = ssmlContent.match(speakContentRegex);

            if (match && match[2]) { // If we have content within <speak> tags
                let innerContent = match[2];
                if (prosodyRegex.test(innerContent)) { // If prosody tag with rate exists inside
                    innerContent = innerContent.replace(prosodyRegex, (prosodyMatch, oldRate) => {
                        return prosodyMatch.replace(`rate="${oldRate}"`, `rate="${rate}"`);
                    });
                } else { // No prosody tag with rate, wrap inner content
                    innerContent = `<prosody rate="${rate}">${innerContent}</prosody>`;
                }
                ssmlContent = `${match[1]}${innerContent}${match[3]}`;
            } else { // Fallback: if <speak> tags are present but structure is unusual or content is empty
                 ssmlContent = `<speak><prosody rate="${rate}">${ssmlContent.replace(/<speak[^>]*>|<\/speak>/gi, '')}</prosody></speak>`;
            }
        }
    }
    return ssmlContent;
}

function updateSynthesizeButtonState() {
    let disabled = true;
    if (currentTTSProvider === 'google') {
        disabled = !G_TTS_API_KEY;
    } else if (currentTTSProvider === 'microsoft') {
        disabled = !MS_TTS_API_KEY || !MS_TTS_SERVICE_REGION;
    }
    dom.aiVoiceSynthesizeBtn.disabled = disabled;
    if (disabled) {
        logStatus(`"${currentTTSProvider === 'google' ? 'Google' : 'Microsoft'} TTS" provider selected, but API key/config is missing. Generation disabled.`, 'error');
    }
}

function handleProviderChange() {
    currentTTSProvider = dom.aiVoiceTTSProviderSelect.value;
    logStatus(`Switched to ${currentTTSProvider === 'google' ? 'Google' : 'Microsoft'} TTS provider.`);
    if (currentTTSProvider === 'google') {
        uiPopulateGoogleVoiceSelectorFunction(dom.aiVoiceTTSVoiceSelect, state.fetchedGTTSVoices, DEFAULT_G_TTS_VOICE_NAME);
    } else if (currentTTSProvider === 'microsoft') {
        uiPopulateMicrosoftVoiceSelectorFunction(dom.aiVoiceTTSVoiceSelect, state.fetchedMsTTSVoices, DEFAULT_MS_TTS_VOICE_NAME);
    }
    updateSynthesizeButtonState();
}


async function handleSynthesizeAudio() {
    updateSynthesizeButtonState(); // Re-check before proceeding
    if (dom.aiVoiceSynthesizeBtn.disabled) {
        logStatus("Cannot synthesize. TTS Provider not correctly configured or API key missing.", "error");
        return;
    }

    const selectedVoiceName = dom.aiVoiceTTSVoiceSelect.value;
    let voiceDetails;
    if (currentTTSProvider === 'google') {
        voiceDetails = state.fetchedGTTSVoices.find(v => v.name === selectedVoiceName);
    } else { // microsoft
        voiceDetails = state.fetchedMsTTSVoices.find(v => v.name === selectedVoiceName);
    }

    if (!voiceDetails) {
        logStatus(`Invalid ${currentTTSProvider} Voice selected.`, "error");
        return;
    }

    const speakingRate = parseFloat(dom.aiVoiceSpeedSlider.value);
    const encodingKey = dom.aiVoiceEncodingSelect.value;
    const encodingConfig = AI_VOICE_CREATOR_ENCODINGS[encodingKey];
    if (!encodingConfig) {
        logStatus("Invalid audio format selected.", "error");
        return;
    }

    const userDefinedPrefix = dom.aiVoiceFilenamePrefix.value.trim();
    dom.aiVoiceSynthesizeBtn.disabled = true; // Disable during processing
    logStatus(`Starting audio generation process using ${currentTTSProvider}...`);



    if (currentProcessingMode === 'single') {
        const ssmlInput = dom.aiVoiceSSMLTextarea.value.trim();
        if (!ssmlInput) {
            logStatus("SSML input is empty for single processing.", "error");
            updateSynthesizeButtonState(); // Re-enable button
            return;
        }
        let baseFilename = userDefinedPrefix || (currentLoadedFile ? currentLoadedFile.name.replace(/\.[^/.]+$/, "") : 'generated_audio');
        baseFilename = baseFilename.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_.-]/g, '');

        await processSingleContent(ssmlInput, baseFilename, voiceDetails, speakingRate, encodingConfig, currentTTSProvider);

    } else if (currentProcessingMode === 'folder') {
        if (batchFilesToProcess.length === 0) {
            logStatus("No files selected for batch processing.", "error");
            updateSynthesizeButtonState(); // Re-enable button
            return;
        }

        let processedCount = 0;
        let skippedCount = 0;
        let errorCount = 0;
        const totalFiles = batchFilesToProcess.length;

        logStatus(`Starting batch processing of ${totalFiles} files...`);

        for (let fileIndex = 0; fileIndex < batchFilesToProcess.length; fileIndex++) {
            const file = batchFilesToProcess[fileIndex];
            const currentFileNum = fileIndex + 1;

            logStatus(`Processing file ${currentFileNum}/${totalFiles}: ${file.name} with ${currentTTSProvider}...`);

            try {
                const fileContent = await readFileContent(file);
                if (!fileContent.trim()) {
                    logStatus(`Skipping empty file ${currentFileNum}/${totalFiles}: ${file.name}.`);
                    skippedCount++;
                    continue;
                }

                let baseFilenameForFile = file.name.replace(/\.[^/.]+$/, "");
                if (userDefinedPrefix) {
                    baseFilenameForFile = `${userDefinedPrefix}_${baseFilenameForFile}`;
                }
                baseFilenameForFile = baseFilenameForFile.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_.-]/g, '');

                await processSingleContent(fileContent, baseFilenameForFile, voiceDetails, speakingRate, encodingConfig, currentTTSProvider);
                processedCount++;
                logStatus(`✅ Completed file ${currentFileNum}/${totalFiles}: ${file.name}`, 'success');

            } catch (error) {
                errorCount++;
                console.error(`Error processing file ${file.name} in batch (${currentFileNum}/${totalFiles}):`, error);
                logStatus(`❌ Error for file ${currentFileNum}/${totalFiles} (${file.name}): ${error.message}`, 'error');

                // Continue processing remaining files even if one fails
                logStatus(`Continuing with remaining ${totalFiles - currentFileNum} files...`);
            }
        }

        // Final summary
        logStatus(`Batch processing complete: ${processedCount} processed, ${skippedCount} skipped, ${errorCount} errors out of ${totalFiles} total files.`,
                 errorCount > 0 ? 'error' : 'success');
    }

    logStatus("Audio generation process complete.");
    updateSynthesizeButtonState(); // Re-enable button based on current config
}

async function processSingleContent(ssmlContent, baseFilename, voiceDetails, speakingRate, encodingConfig, provider) {
    const ssmlChunks = ssmlContent.split(SSML_BREAKPOINT_MARKER).map(s => s.trim()).filter(s => s.length > 0);

    if (ssmlChunks.length === 0 && ssmlContent.length > 0) {
        ssmlChunks.push(ssmlContent);
    }
    if (ssmlChunks.length === 0) {
        logStatus(`No content to process for ${baseFilename} using ${provider}.`, 'error');
        return;
    }

    for (let i = 0; i < ssmlChunks.length; i++) {
        const chunk = ssmlChunks[i];
        const partNumberSuffix = ssmlChunks.length > 1 ? `_part_${i + 1}` : '';
        const filename = `${baseFilename}${partNumberSuffix}${encodingConfig.extension}`;

        logStatus(`Synthesizing chunk ${i + 1}/${ssmlChunks.length} for "${filename}" using ${provider}...`);

        try {
            const fullSsmlToSynthesize = prepareSSMLForSynthesis(chunk, speakingRate);

            const synthesizedAudioData = await ttsService.synthesizeTextToAudio(
                fullSsmlToSynthesize,
                voiceDetails.name,
                provider,
                baseFilename + partNumberSuffix,
                encodingConfig.extension,
                encodingConfig.apiValue,
                encodingConfig.mimeType
            );

            downloadBlob(synthesizedAudioData.file, filename);
            logStatus(`Successfully generated and downloaded "${filename}" using ${provider}.`, "success");

        } catch (error) {
            console.error(`Error synthesizing chunk ${i + 1} ("${filename}") with ${provider}:`, error);
            logStatus(`Error for chunk ${i + 1} ("${filename}") with ${provider}: ${error.message}`, 'error');
        }
    }
}



// Remove SSML tags and return plain text while preserving real content
// Safer approach: pre-tokenize <break>, parse as XML/HTML to strip tags, then restore breaks
function removeSSMLTagsToPlainText(text) {
    if (typeof text !== 'string') return '';

    // Normalize line endings early
    let src = text.replace(/\r\n?/g, '\n');

    // 1) Decode common HTML entities so encoded tags become parsable (<, >, &)
    // Use a DOM-based decode to avoid double-decoding issues
    try {
        const ta = document.createElement('textarea');
        ta.innerHTML = src;
        src = ta.value;
    } catch { /* ignore if DOM not available */ }

    // 2) Pre-tokenize <break .../> to preserve pause semantics through parsing
    // Tokens look like [[__BREAK__ time="1s" strength="strong"]]
    src = src.replace(/<\s*break\b([^>]*)\/?\s*>/gi, (m, attrs = '') => {
        const clean = String(attrs).replace(/\s+/g, ' ').trim();
        return `[[__BREAK__ ${clean}]]`;
    });

    // 3) Strip tags by parsing as XML first; if that fails, try HTML; else regex fallback
    function parseAsXml(str) {
        try {
            const parser = new DOMParser();
            const doc = parser.parseFromString(str, 'text/xml');
            if (doc.getElementsByTagName('parsererror').length) return null;
            return doc;
        } catch { return null; }
    }
    let plain = '';
    let doc = parseAsXml(src);
    if (!doc) {
        // Try wrapping in a root to handle multiple top-level nodes
        doc = parseAsXml(`<root>${src}</root>`);
    }
    if (doc) {
        plain = doc.documentElement.textContent || '';
    } else {
        // Fallback: conservative regex strip (only removes angle-bracketed tags)
        plain = src.replace(/<[^>]*>/g, '');
    }

    // 4) Restore break semantics from tokens (newline for longer/stronger pauses, else space)
    plain = plain.replace(/\[\[__BREAK__(.*?)\]\]/g, (m, attrs) => {
        const a = attrs || '';
        let sep = ' ';
        const timeMatch = a.match(/time\s*=\s*"([^"]+)"|time\s*=\s*'([^']+)'|time\s*=\s*([^\s"']+)/i);
        const strengthMatch = a.match(/strength\s*=\s*"([^"]+)"|strength\s*=\s*'([^']+)'|strength\s*=\s*([^\s"']+)/i);
        const timeVal = timeMatch ? (timeMatch[1] || timeMatch[2] || timeMatch[3]) : null;
        const strengthVal = strengthMatch ? (strengthMatch[1] || strengthMatch[2] || strengthMatch[3]) : null;
        if (timeVal) {
            const s = /^\s*([\d.]+)\s*s\s*$/i.exec(timeVal);
            const ms = /^\s*([\d.]+)\s*ms\s*$/i.exec(timeVal);
            if ((s && parseFloat(s[1]) >= 1) || (ms && parseFloat(ms[1]) >= 1000)) sep = '\n';
        }
        if (strengthVal) {
            const sv = String(strengthVal).toLowerCase();
            if (sv === 'medium' || sv === 'strong' || sv === 'x-strong') sep = '\n';
        }
        return sep;
    });

    // 5) Remove known orphan SSML attribute fragments only when they include a closing '>'
    // This avoids accidental removal in words like "irritate" or normal quoted text.
    plain = plain.replace(/\b(?:time|strength|pitch|rate|volume)\s*=\s*"[^"]*"\s*\/?>(?=\s|$)/gi, '');
    plain = plain.replace(/\b\d+(?:\.\d+)?\s*(?:ms|s)"\s*\/?>(?=\s|$)/gi, '');

    // 6) Whitespace normalization: keep paragraph breaks, collapse runs of spaces
    plain = plain
        .replace(/[\t\f\v]+/g, ' ')
        .replace(/\u00A0/g, ' ')
        .replace(/ *\n+ */g, '\n')
        .replace(/\n{3,}/g, '\n\n')
        .replace(/ {2,}/g, ' ')
        .trim();

    return plain;
}

async function handleExportCleanText() {
    if (currentProcessingMode !== 'folder' || batchFilesToProcess.length === 0) {
        logStatus("Switch to 'Batch' mode and select a folder before exporting clean text.", 'error');
        return;
    }

    logStatus('Starting clean text export...');

    // Try to get write access to the original folder using File System Access API
    let rootDirHandle = null;
    let useDownloadFallback = false;

    if ('showDirectoryPicker' in window) {
        try {
            logStatus('Please select the ORIGINAL folder to create the "clean_text" subfolder in.');
            rootDirHandle = await window.showDirectoryPicker({ mode: 'readwrite' });
        } catch (err) {
            logStatus('Folder access was cancelled or blocked. Falling back to file downloads.', 'error');
            useDownloadFallback = true;
        }
    } else {
        useDownloadFallback = true;
        logStatus('This browser does not support folder write access. Falling back to downloads.');
    }

    let cleanDirHandle = null;
    if (!useDownloadFallback && rootDirHandle) {
        try {
            cleanDirHandle = await rootDirHandle.getDirectoryHandle('clean_text', { create: true });
        } catch (err) {
            logStatus(`Could not create/open "clean_text" subfolder: ${err.message}`, 'error');
            useDownloadFallback = true;
        }
    }

    let processed = 0;
    let failed = 0;

    for (let i = 0; i < batchFilesToProcess.length; i++) {
        const file = batchFilesToProcess[i];
        const ext = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
        if (!SUPPORTED_DOC_EXTENSIONS.includes(ext)) continue;

        const baseName = file.name.replace(/\.[^/.]+$/, '');
        const targetName = `${baseName}.txt`;

        logStatus(`Cleaning ${i + 1}/${batchFilesToProcess.length}: ${file.name}`);
        try {
            const rawContent = await readFileContent(file);
            const cleaned = removeSSMLTagsToPlainText(rawContent);

            if (useDownloadFallback || !cleanDirHandle) {
                const blob = new Blob([cleaned], { type: 'text/plain;charset=utf-8' });
                downloadBlob(blob, targetName);
            } else {
                const fileHandle = await cleanDirHandle.getFileHandle(targetName, { create: true });
                const writable = await fileHandle.createWritable();
                await writable.write(cleaned);
                await writable.close();
            }

            processed++;
        } catch (err) {
            failed++;
            console.error('Clean export error for', file.name, err);
            logStatus(`Failed to process ${file.name}: ${err.message}`, 'error');
        }
    }

    if (processed > 0) {
        if (!useDownloadFallback && rootDirHandle) {
            logStatus(`Export complete: ${processed} file(s) saved to "clean_text" subfolder. ${failed > 0 ? failed + ' failed.' : ''}`, failed > 0 ? 'error' : 'success');
        } else {
            logStatus(`Export complete: ${processed} file(s) downloaded as clean .txt. ${failed > 0 ? failed + ' failed.' : ''}`, failed > 0 ? 'error' : 'success');
        }
    } else {
        logStatus('No files were processed.', 'error');
    }
}

export function initAiVoiceCreator(uiHelpers) {
    uiUpdateStatusMain = uiHelpers.updateStatus;
    uiPopulateGoogleVoiceSelectorFunction = uiHelpers.populateGoogleVoiceSelector;
    uiPopulateMicrosoftVoiceSelectorFunction = uiHelpers.populateMicrosoftVoiceSelector;

    // Populate encoding selector
    if (dom.aiVoiceEncodingSelect) {
        populateEncodingSelector(dom.aiVoiceEncodingSelect, 'mp3');
    }

    if (!dom.aiVoiceCreatorBtn) {
        console.warn("AI Voice Creator DOM elements not fully initialized.");
        return;
    }

    if (dom.aiVoiceModeSingleRadio) {
        dom.aiVoiceModeSingleRadio.addEventListener('change', () => updateUIVisibilityForMode('single'));
    }
    if (dom.aiVoiceModeFolderRadio) {
        dom.aiVoiceModeFolderRadio.addEventListener('change', () => updateUIVisibilityForMode('folder'));
    }
    updateUIVisibilityForMode(dom.aiVoiceModeSingleRadio.checked ? 'single' : 'folder');

    if (dom.aiVoiceTTSProviderSelect) {
        dom.aiVoiceTTSProviderSelect.addEventListener('change', handleProviderChange);
        // Initial population based on default provider
        currentTTSProvider = dom.aiVoiceTTSProviderSelect.value;
    }

    // Initial voice population:
    if (currentTTSProvider === 'google') {
        uiPopulateGoogleVoiceSelectorFunction(dom.aiVoiceTTSVoiceSelect, state.fetchedGTTSVoices, DEFAULT_G_TTS_VOICE_NAME);
    } else if (currentTTSProvider === 'microsoft') {
         uiPopulateMicrosoftVoiceSelectorFunction(dom.aiVoiceTTSVoiceSelect, state.fetchedMsTTSVoices, DEFAULT_MS_TTS_VOICE_NAME);
    }


    if (dom.aiVoiceSSMLFileInput) {
        dom.aiVoiceSSMLFileInput.addEventListener('change', handleSSMLFileUpload);
    }
    if (dom.aiVoiceSSMLFolderInput) {
        dom.aiVoiceSSMLFolderInput.addEventListener('change', handleSSMLFolderUpload);
    }
    if (dom.aiVoiceLoadFromEditorBtn) {
        dom.aiVoiceLoadFromEditorBtn.addEventListener('click', handleLoadFromEditor);
    }

    if (dom.aiVoiceSpeedSlider && dom.aiVoiceSpeedValueDisplay) {
        dom.aiVoiceSpeedSlider.addEventListener('input', (e) => {
            dom.aiVoiceSpeedValueDisplay.textContent = parseFloat(e.target.value).toFixed(2);
        });
        dom.aiVoiceSpeedValueDisplay.textContent = parseFloat(dom.aiVoiceSpeedSlider.value).toFixed(2);
    }

    if (dom.aiVoiceSynthesizeBtn) {
        dom.aiVoiceSynthesizeBtn.addEventListener('click', handleSynthesizeAudio);
    }
    if (dom.aiVoiceExportCleanTextBtn) {
        dom.aiVoiceExportCleanTextBtn.addEventListener('click', handleExportCleanText);
        dom.aiVoiceExportCleanTextBtn.disabled = !(currentProcessingMode === 'folder' && batchFilesToProcess.length > 0);
    }

    updateSynthesizeButtonState(); // Set initial state of synthesize button
    logStatus("AI Voice Creator initialized. Ready.");
}