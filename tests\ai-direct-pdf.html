<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>AI Direct PDF</title>
    <link rel="stylesheet" href="../styles/themes.css" />
    <link rel="stylesheet" href="../styles/base.css" />
    <link rel="stylesheet" href="../styles/controls.css" />
    <link rel="stylesheet" href="../styles/ai-direct-pdf.css" />
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js" defer></script>
  </head>
  <body>
    <div id="app-root">
      <div class="app-header">
        <div class="app-brand">Roman Catholic Audiobooks</div>
        <a href="../index.html" class="header-nav-button" style="text-decoration:none; display:inline-flex; align-items:center;">
          <span class="material-icons" style="margin-right:6px;">arrow_back</span> Back to Home
        </a>
        <span class="header-nav-button" style="pointer-events:none; opacity:0.8;">AI Direct PDF</span>
      </div>

      <div class="app-container" style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; padding: 12px; align-items: start;">
        <div class="panel" role="region" aria-label="Narration Controls">
          <h2 style="margin-top:0;">Narration Controls</h2>
          <div class="button-group" style="margin-bottom:8px; display:flex; gap:8px; flex-wrap:wrap;">
            <label class="control-button" for="aidpdf-file-input"><span class="material-icons">attach_file</span> Select PDF</label>
            <input type="file" id="aidpdf-file-input" accept="application/pdf" class="visually-hidden" />
            <button id="aidpdf-open-sample" type="button" class="control-button"><span class="material-icons">library_books</span> Open Sample</button>
          </div>
          <div class="button-group" style="margin-bottom:8px; display:flex; gap:12px; flex-wrap:wrap; align-items:center;">
            <label>Provider <select id="aidpdf-provider"></select></label>
            <label>Voice <select id="aidpdf-voice"></select></label>
            <label>Speed <input id="aidpdf-speed" type="range" min="0.5" max="1.5" step="0.05" value="1.0" /> <span id="aidpdf-speed-value">1.00×</span></label>
            <button id="aidpdf-refresh-voices" type="button" class="control-button"><span class="material-icons">refresh</span> Refresh Voices</button>
          </div>
          <div class="button-group" style="margin-bottom:8px; display:flex; gap:12px; flex-wrap:wrap; align-items:center;">
            <label style="display:flex; gap:6px; align-items:center;">
              <input type="checkbox" id="aidpdf-use-gemini" /> Use Gemini Vision narration
            </label>
            <label>Gemini Model
              <select id="aidpdf-gemini-model">
                <option value="gemini-2.5-flash" selected>Gemini 2.5 Flash</option>
                <option value="gemini-2.5-pro">Gemini 2.5 Pro</option>
              </select>
            </label>
          </div>
          <div class="button-group" style="margin-bottom:8px; display:flex; gap:12px; flex-wrap:wrap; align-items:center;">
            <label>Style
              <select id="aidpdf-style">
                <option value="neutral" selected>Neutral</option>
                <option value="reverent">Reverent devotional</option>
                <option value="dramatic">Dramatic storytelling</option>
                <option value="academic">Academic</option>
                <option value="pro-audiobook">Professional audiobook</option>
              </select>
            </label>
          </div>
          <div class="button-group" style="margin-bottom:8px; display:flex; gap:12px; flex-wrap:wrap; align-items:flex-start;">
            <label style="display:flex; flex-direction:column; gap:6px; width:100%; max-width:720px;">
              Narration instructions (natural language)
              <textarea id="aidpdf-instructions" rows="3" placeholder="Examples: emphasize quotes; insert brief pause at commas; more dramatic narration."></textarea>
              <small style="opacity:0.8;">These instructions apply simple SSML rules (e.g., emphasize quoted text, add short pauses at commas). For advanced control, use the Custom SSML field below.</small>
            </label>
          </div>
          <div class="button-group" style="margin-bottom:8px; display:flex; gap:12px; flex-wrap:wrap; align-items:flex-start;">
            <label style="display:flex; flex-direction:column; gap:6px; width:100%; max-width:720px;">
              Custom SSML (optional)
              <textarea id="aidpdf-ssml" rows="3" placeholder="Example: &lt;break strength='medium'/&gt; or &lt;emphasis level='moderate'&gt;{{after}}&lt;/emphasis&gt;"></textarea>
              <small style="opacity:0.8;">This snippet will be injected inside the SSML prosody for each sentence. Use SSML tags only. Do not include &lt;speak&gt; or &lt;voice&gt; here.</small>
            </label>
          </div>
          <div class="button-group playback-controls" style="margin-bottom:8px; display:flex; gap:8px; flex-wrap:wrap;">
            <button id="aidpdf-play" type="button" class="control-button"><span class="material-icons">play_arrow</span> Play</button>
            <button id="aidpdf-pause" type="button" class="control-button"><span class="material-icons">pause</span> Pause</button>
            <button id="aidpdf-stop" type="button" class="control-button"><span class="material-icons">stop</span> Stop</button>
            <button id="aidpdf-skip-back" type="button" class="control-button"><span class="material-icons">replay_10</span></button>
            <button id="aidpdf-skip-fwd" type="button" class="control-button"><span class="material-icons">forward_10</span></button>
            <button id="aidpdf-download" type="button" class="control-button"><span class="material-icons">download</span> Download MP3</button>
          </div>
          <div class="progress-section" style="display:flex; gap:8px; align-items:center;">
            <span id="aidpdf-current-time">0:00</span>
            <input id="aidpdf-progress" class="progress-bar" type="range" min="0" max="0" step="0.01" value="0" />
            <span id="aidpdf-total-time">0:00</span>
          </div>
        </div>

        <div class="panel" role="region" aria-label="PDF Viewer">
          <h2 style="margin-top:0;">PDF Viewer</h2>
          <div class="button-group" style="display:flex; gap:8px; align-items:center; margin-bottom:8px;">
            <button id="aidpdf-prev-page" type="button" class="control-button"><span class="material-icons">chevron_left</span></button>
            <input id="aidpdf-page-number" type="number" value="1" min="1" style="width:70px;" />
            <span id="aidpdf-page-count">/ 0</span>
            <button id="aidpdf-next-page" type="button" class="control-button"><span class="material-icons">chevron_right</span></button>
          </div>
          <div class="pdf-view" style="position:relative; overflow:auto; border:1px solid var(--border-secondary); background:var(--bg-panel); max-height: 70vh;">
            <div class="pdf-canvas-wrap">
              <canvas id="aidpdf-canvas"></canvas>
              <div id="aidpdf-text-layer" class="pdf-textlayer textLayer"></div>
            </div>
          </div>
        </div>

        <div class="panel" role="region" aria-label="Live Transcript">
          <h2 style="margin-top:0;">Live Transcript</h2>
          <div class="button-group" style="margin-bottom:8px; display:flex; gap:8px;">
            <button id="aidpdf-regenerate" type="button" class="control-button" disabled>Regenerate Selected</button>
          </div>
          <div id="aidpdf-transcript" class="transcript" style="max-height: 70vh;"></div>
        </div>
      </div>
      <div class="status-bar">AI Direct PDF Ready.</div>
    </div>

    <script type="module">
      import '../constants.js';
      import '../utils.js';
    </script>
    <script type="module" src="../services/aiDirectPdf.js"></script>
  </body>
</html>


