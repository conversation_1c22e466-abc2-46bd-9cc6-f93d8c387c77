<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug TTS Save Issue</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #e9ecef;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>Debug TTS Save Issue</h1>
    
    <div class="debug-section">
        <h2>Debug Instructions</h2>
        <div class="step">
            <strong>Step 1:</strong> Open the main application and load an audio file
        </div>
        <div class="step">
            <strong>Step 2:</strong> Click "Reprocess with Google TTS" or "Reprocess with Microsoft TTS"
        </div>
        <div class="step">
            <strong>Step 3:</strong> Open browser console (F12) to see debug messages
        </div>
        <div class="step">
            <strong>Step 4:</strong> Click the "Save & Overwrite Original" button
        </div>
        <div class="step">
            <strong>Step 5:</strong> Check the console output and the debug information below
        </div>
    </div>

    <div class="debug-section">
        <h2>Current State Debug</h2>
        <button onclick="debugCurrentState()">Check Current State</button>
        <div id="state-debug"></div>
    </div>

    <div class="debug-section">
        <h2>Music Files Debug</h2>
        <button onclick="debugMusicFiles()">Check Music Files</button>
        <div id="music-files-debug"></div>
    </div>

    <div class="debug-section">
        <h2>TTS Save State Debug</h2>
        <button onclick="debugTtsSaveState()">Check TTS Save State</button>
        <div id="tts-save-debug"></div>
    </div>

    <div class="debug-section">
        <h2>Manual Test Save Function</h2>
        <button onclick="testSaveFunction()">Test Save Function</button>
        <div id="save-test-debug"></div>
    </div>

    <script type="module">
        // Import modules for debugging
        import * as state from '../state.js';
        import * as ttsService from '../services/ttsService.js';
        import * as dom from '../domElements.js';
        
        window.state = state;
        window.ttsService = ttsService;
        window.dom = dom;
        
        window.debugCurrentState = function() {
            const debugDiv = document.getElementById('state-debug');
            const info = {
                currentTrackId: state.currentTrackId,
                currentAudioFile: state.currentAudioFile ? {
                    id: state.currentAudioFile.id,
                    name: state.currentAudioFile.name,
                    isSynthesized: state.currentAudioFile.isSynthesized
                } : null,
                isPlaying: state.isPlaying,
                musicFilesCount: state.musicFiles.length
            };
            
            debugDiv.innerHTML = '<pre>' + JSON.stringify(info, null, 2) + '</pre>';
        };
        
        window.debugMusicFiles = function() {
            const debugDiv = document.getElementById('music-files-debug');
            const files = state.musicFiles.map(f => ({
                id: f.id,
                name: f.name,
                isSynthesized: f.isSynthesized,
                ttsProvider: f.ttsProvider,
                duration: f.duration
            }));
            
            debugDiv.innerHTML = '<pre>' + JSON.stringify(files, null, 2) + '</pre>';
        };
        
        window.debugTtsSaveState = function() {
            const debugDiv = document.getElementById('tts-save-debug');
            const saveState = state.latestTtsAudioForSave;
            
            if (!saveState) {
                debugDiv.innerHTML = '<div class="error">No TTS save state found</div>';
                return;
            }
            
            const info = {
                provider: saveState.provider,
                originalAudioId: saveState.originalAudioId,
                originalAudioFile: saveState.originalAudioFile ? {
                    id: saveState.originalAudioFile.id,
                    name: saveState.originalAudioFile.name
                } : null,
                audioEntry: saveState.audioEntry ? {
                    id: saveState.audioEntry.id,
                    name: saveState.audioEntry.name,
                    duration: saveState.audioEntry.duration
                } : null
            };
            
            debugDiv.innerHTML = '<pre>' + JSON.stringify(info, null, 2) + '</pre>';
        };
        
        window.testSaveFunction = async function() {
            const debugDiv = document.getElementById('save-test-debug');
            
            if (!state.latestTtsAudioForSave) {
                debugDiv.innerHTML = '<div class="error">No TTS audio available to save. Please run TTS reprocessing first.</div>';
                return;
            }
            
            try {
                debugDiv.innerHTML = '<div>Testing save function... Check console for detailed logs.</div>';
                console.log('=== MANUAL SAVE TEST START ===');
                console.log('Current state before save:', {
                    latestTtsAudioForSave: state.latestTtsAudioForSave,
                    musicFiles: state.musicFiles.map(f => ({ id: f.id, name: f.name })),
                    currentTrackId: state.currentTrackId
                });
                
                await ttsService.handleSaveTtsOverwrite();
                
                console.log('Current state after save:', {
                    latestTtsAudioForSave: state.latestTtsAudioForSave,
                    musicFiles: state.musicFiles.map(f => ({ id: f.id, name: f.name })),
                    currentTrackId: state.currentTrackId
                });
                console.log('=== MANUAL SAVE TEST END ===');
                
                debugDiv.innerHTML = '<div class="success">Save function executed. Check console for details and verify the playlist.</div>';
            } catch (error) {
                console.error('Save test error:', error);
                debugDiv.innerHTML = '<div class="error">Save test failed: ' + error.message + '</div>';
            }
        };
        
        // Auto-refresh debug info every 5 seconds
        setInterval(() => {
            if (document.getElementById('state-debug').innerHTML) {
                debugCurrentState();
            }
            if (document.getElementById('music-files-debug').innerHTML) {
                debugMusicFiles();
            }
            if (document.getElementById('tts-save-debug').innerHTML) {
                debugTtsSaveState();
            }
        }, 5000);
    </script>

    <div class="debug-section">
        <h2>Expected Console Messages</h2>
        <p>When TTS reprocessing works correctly, you should see:</p>
        <ul>
            <li><code>updateMusicFileListWithNewTTS called for [Provider]</code></li>
            <li><code>[Provider] TTS: Storing original audio ID: [ID] for file: [filename]</code></li>
            <li><code>[Provider] TTS: Set latestTtsAudioForSave: [object]</code></li>
        </ul>
        
        <p>When save function works correctly, you should see:</p>
        <ul>
            <li><code>handleSaveTtsOverwrite called</code></li>
            <li><code>Looking for original audio with ID: [ID]</code></li>
            <li><code>Found original file by ID: [object]</code></li>
            <li><code>Starting file replacement process...</code></li>
            <li><code>File replacement completed successfully</code></li>
        </ul>
    </div>

    <div class="debug-section">
        <h2>Common Issues</h2>
        <ul>
            <li><strong>Save button disabled:</strong> Check if latestTtsAudioForSave is set</li>
            <li><strong>Original file not found:</strong> Check if the original audio ID still exists in musicFiles</li>
            <li><strong>No confirmation dialog:</strong> Check if handleSaveTtsOverwrite is being called</li>
            <li><strong>File not replaced:</strong> Check console for replacement process logs</li>
        </ul>
    </div>
</body>
</html>
