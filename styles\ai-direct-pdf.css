:root { --gap: 12px; }

body { margin: 0; font-family: system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif; }

#aidpdf-app { display: flex; flex-direction: column; height: 100vh; }

.aidpdf-header { padding: 12px 16px; border-bottom: 1px solid #e1e4e8; background: #fafbfc; display: grid; gap: var(--gap); }
.aidpdf-header h1 { margin: 0; font-size: 18px; }
.file-controls, .voice-controls, .transport-controls { display: flex; gap: var(--gap); align-items: center; flex-wrap: wrap; }
.voice-controls label { display: flex; gap: 8px; align-items: center; }
.progress { display: flex; gap: 8px; align-items: center; min-width: 320px; flex: 1; }
.progress input[type="range"] { flex: 1; }

.aidpdf-main { display: grid; grid-template-columns: 1fr 1fr; gap: var(--gap); padding: var(--gap); height: calc(100vh - 160px); }

.pdf-pane, .transcript-pane { display: flex; flex-direction: column; min-height: 0; }
.pdf-toolbar, .transcript-toolbar { display: flex; gap: var(--gap); align-items: center; padding-bottom: 8px; border-bottom: 1px solid #e1e4e8; }
.pdf-view { position: relative; overflow: auto; flex: 1; }
.pdf-canvas-wrap { position: relative; margin: 0 auto; }
#aidpdf-canvas { display: block; background: #fff; }
#aidpdf-text-layer { position: absolute; left: 0; top: 0; right: 0; bottom: 0; pointer-events: none; }
.textLayer span { position: absolute; white-space: pre; transform-origin: 0% 0%; color: transparent; }
.textLayer span.match-highlight { background: rgba(255, 230, 0, 0.5); border-radius: 2px; }
.textLayer span.reading-highlight { background: rgba(112, 190, 255, 0.35); border-radius: 2px; }

.transcript { overflow: auto; flex: 1; padding-right: 8px; }
.transcript .seg { padding: 6px 8px; border-left: 3px solid transparent; margin-bottom: 2px; }
.transcript .seg.active { border-left-color: #0077ff; background: #f0f7ff; }
.transcript .seg .meta { color: #666; font-size: 12px; margin-bottom: 4px; display: flex; gap: 8px; }
.transcript .seg .text { outline: none; white-space: pre-wrap; }

/* Custom SSML input */
#aidpdf-ssml {
  width: 100%;
  min-height: 60px;
  resize: vertical;
}

#aidpdf-instructions {
  width: 100%;
  min-height: 60px;
  resize: vertical;
}

@media (max-width: 1100px) {
  .aidpdf-main { grid-template-columns: 1fr; height: auto; }
}



