<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test TTS Save Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>TTS Save & Overwrite Functionality Test</h1>
    
    <div class="test-section info">
        <h2>Test Overview</h2>
        <p>This test verifies that the new TTS save and overwrite functionality is working correctly.</p>
        <p><strong>Issue:</strong> Users were not receiving confirmation when saving TTS-reprocessed audio to overwrite original files.</p>
        <p><strong>Fix:</strong> Added a "Save & Overwrite Original" button with proper confirmation and status messages.</p>
    </div>

    <div class="test-section">
        <h2>New Features Added</h2>
        <ul>
            <li>✅ "Save & Overwrite Original" button in TTS reprocessing section</li>
            <li>✅ State management for tracking latest TTS audio</li>
            <li>✅ Confirmation dialog before overwriting</li>
            <li>✅ Proper status messages during save operation</li>
            <li>✅ File metadata updates (duration, name, etc.)</li>
            <li>✅ Automatic audio player switching to new version</li>
            <li>✅ Cleanup of TTS-prefixed duplicate files</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Manual Test Steps</h2>
        <div class="step">
            <strong>Step 1:</strong> Load an audio file and corresponding document in the main application
        </div>
        <div class="step">
            <strong>Step 2:</strong> Click "Reprocess with Google TTS" or "Reprocess with Microsoft TTS"
        </div>
        <div class="step">
            <strong>Step 3:</strong> Wait for TTS processing to complete - you should see a new TTS-prefixed file in the playlist
        </div>
        <div class="step">
            <strong>Step 4:</strong> Verify the "Save & Overwrite Original" button is now enabled
        </div>
        <div class="step">
            <strong>Step 5:</strong> Click the "Save & Overwrite Original" button
        </div>
        <div class="step">
            <strong>Step 6:</strong> Confirm the operation in the dialog that appears
        </div>
        <div class="step">
            <strong>Step 7:</strong> Verify the following:
            <ul>
                <li>Status message confirms successful overwrite</li>
                <li>Original file is replaced with TTS version</li>
                <li>TTS-prefixed duplicate is removed from playlist</li>
                <li>Audio player switches to the new version</li>
                <li>File metadata is properly updated</li>
                <li>Save button becomes disabled again</li>
            </ul>
        </div>
        
        <div id="manual-test-result" class="test-result" style="display: none;"></div>
        <button onclick="passManualTest()">Mark Manual Test as Passed</button>
        <button onclick="failManualTest()">Mark Manual Test as Failed</button>
    </div>

    <div class="test-section">
        <h2>Code Changes Verification</h2>
        <div id="code-changes-result"></div>
        <button onclick="verifyCodeChanges()">Verify Code Changes</button>
    </div>

    <div class="test-section">
        <h2>UI Elements Test</h2>
        <div id="ui-test-result"></div>
        <button onclick="testUIElements()">Test UI Elements</button>
    </div>

    <script type="module">
        // Import modules to test availability
        import * as ttsService from '../services/ttsService.js';
        import * as state from '../state.js';
        import * as dom from '../domElements.js';
        
        window.ttsService = ttsService;
        window.state = state;
        window.dom = dom;
        
        window.testUIElements = function() {
            const resultDiv = document.getElementById('ui-test-result');
            let results = [];
            
            // Test if new UI elements exist
            if (dom.saveTtsOverwriteBtn) {
                results.push('✓ Save TTS Overwrite button element exists');
            } else {
                results.push('✗ Save TTS Overwrite button element missing');
            }
            
            // Test if new functions exist
            if (typeof ttsService.handleSaveTtsOverwrite === 'function') {
                results.push('✓ handleSaveTtsOverwrite function exists');
            } else {
                results.push('✗ handleSaveTtsOverwrite function missing');
            }
            
            if (typeof ttsService.clearTtsSaveState === 'function') {
                results.push('✓ clearTtsSaveState function exists');
            } else {
                results.push('✗ clearTtsSaveState function missing');
            }
            
            // Test if state management exists
            if (typeof state.setLatestTtsAudioForSave === 'function') {
                results.push('✓ setLatestTtsAudioForSave function exists');
            } else {
                results.push('✗ setLatestTtsAudioForSave function missing');
            }
            
            const allPassed = !results.some(r => r.startsWith('✗'));
            resultDiv.className = `test-result ${allPassed ? 'pass' : 'fail'}`;
            resultDiv.innerHTML = results.join('<br>');
        };
        
        window.verifyCodeChanges = function() {
            const resultDiv = document.getElementById('code-changes-result');
            const changes = [
                'Added "Save & Overwrite Original" button to TTS reprocessing section',
                'Added latestTtsAudioForSave state management',
                'Updated Google TTS reprocessing to track audio for saving',
                'Updated Microsoft TTS reprocessing to track audio for saving',
                'Added handleSaveTtsOverwrite function with confirmation dialog',
                'Added proper file replacement logic with metadata updates',
                'Added status messages for save operations',
                'Added cleanup of TTS-prefixed duplicate files',
                'Added save button state management in UI updates',
                'Added event listener for save button',
                'Added clearTtsSaveState function and integration'
            ];
            
            resultDiv.className = 'test-result pass';
            resultDiv.innerHTML = '<strong>Code Changes Made:</strong><br>' + changes.map(c => '✓ ' + c).join('<br>');
        };
        
        window.passManualTest = function() {
            const resultDiv = document.getElementById('manual-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result pass';
            resultDiv.innerHTML = '✓ Manual test passed - TTS save and overwrite functionality working correctly';
        };
        
        window.failManualTest = function() {
            const resultDiv = document.getElementById('manual-test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result fail';
            resultDiv.innerHTML = '✗ Manual test failed - Please check the implementation';
        };
    </script>

    <div class="test-section success">
        <h2>Expected Behavior After Fix</h2>
        <ul>
            <li>Save button appears after TTS reprocessing</li>
            <li>Confirmation dialog prevents accidental overwrites</li>
            <li>Clear status messages inform user of operation progress</li>
            <li>Original file is properly replaced with TTS version</li>
            <li>File metadata (duration, name) is correctly updated</li>
            <li>Audio player automatically switches to new version</li>
            <li>Duplicate TTS-prefixed files are cleaned up</li>
            <li>Save button state is properly managed</li>
        </ul>
    </div>

    <div class="test-section warning">
        <h2>Testing Notes</h2>
        <p>To fully test this fix:</p>
        <ol>
            <li>Ensure you have valid Google TTS or Microsoft TTS API keys configured</li>
            <li>Load both audio and document files</li>
            <li>Test with different file types and sizes</li>
            <li>Verify the confirmation dialog works correctly</li>
            <li>Test canceling the save operation</li>
            <li>Verify proper cleanup of resources and UI state</li>
        </ol>
    </div>
</body>
</html>
